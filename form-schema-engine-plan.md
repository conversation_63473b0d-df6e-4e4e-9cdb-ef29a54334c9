# Form Schema Engine Implementation Plan

## Phase 1: Core Schema Engine (Week 1-2)

### 1.1 Create Base Schema Types
```typescript
// src/types/FormSchema.ts
export interface BaseFieldSchema {
  id: string;
  type: FieldType;
  label: string;
  required?: boolean;
  disabled?: boolean;
  visible?: boolean;
  placeholder?: string;
  validation?: ValidationRule[];
  dependencies?: FieldDependency[];
  containerClassName?: string;
  labelClassName?: string;
  inputClassName?: string;
}

export interface TextFieldSchema extends BaseFieldSchema {
  type: 'text' | 'email' | 'password' | 'textarea';
  maxLength?: number;
  minLength?: number;
}

export interface NumberFieldSchema extends BaseFieldSchema {
  type: 'number' | 'currency';
  min?: number;
  max?: number;
  step?: number;
  precision?: number;
}

export interface SelectFieldSchema extends BaseFieldSchema {
  type: 'select' | 'multiselect';
  options: SelectOption[];
  searchable?: boolean;
  allowClear?: boolean;
}

export interface CustomFieldSchema extends BaseFieldSchema {
  type: 'custom';
  component: string; // 'DocumentNumberSeqInput', 'SelectSellerV2', etc.
  props: Record<string, any>;
}

export interface FormSchema {
  id: string;
  title: string;
  sections: FormSection[];
  validation?: FormValidation;
  layout?: FormLayout;
}
```

### 1.2 Create Form Renderer Hook
```typescript
// src/hooks/useFormRenderer.ts
export const useFormRenderer = (schema: FormSchema, initialData?: any) => {
  const [formData, setFormData] = useState(initialData || {});
  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});

  const updateField = useCallback((fieldId: string, value: any) => {
    setFormData(prev => ({ ...prev, [fieldId]: value }));
    // Clear error when user starts typing
    if (errors[fieldId]) {
      setErrors(prev => ({ ...prev, [fieldId]: null }));
    }
  }, [errors]);

  const validateForm = useCallback(() => {
    // Schema-based validation logic
  }, [schema, formData]);

  const renderField = useCallback((field: BaseFieldSchema) => {
    // Dynamic field rendering based on schema
  }, [formData, errors, touched]);

  return {
    formData,
    errors,
    touched,
    updateField,
    validateForm,
    renderField,
    setFormData,
    resetForm: () => setFormData(initialData || {}),
  };
};
```

### 1.3 Create Field Component Registry
```typescript
// src/components/Common/FormRenderer/FieldRegistry.ts
export const FIELD_COMPONENTS = {
  text: H3FormInput,
  number: H3FormInput,
  email: H3FormInput,
  select: PRZSelect,
  date: DatePicker,
  // Custom components
  documentSequence: DocumentNumberSeqInput,
  sellerSelector: SelectSellerV2,
  addressSelector: AddressSelector,
  tenantSelector: TenantSelector,
  // Add more as needed
};
```

## Phase 2: Form Schema Definitions (Week 2-3)

### 2.1 Create GRN Form Schema
```typescript
// src/schemas/GRNFormSchema.ts
export const GRNFormSchema: FormSchema = {
  id: 'grn-form',
  title: 'Goods Receiving Note',
  sections: [
    {
      id: 'basic-info',
      title: 'Basic Information',
      layout: { columns: 2 },
      fields: [
        {
          id: 'grnNumber',
          type: 'custom',
          component: 'documentSequence',
          label: 'GRN Number',
          required: true,
          props: {
            entityName: 'GOOD_RECEIVING',
            docTitle: 'GRN#',
          }
        },
        {
          id: 'selectedTenant',
          type: 'custom',
          component: 'tenantSelector',
          label: 'Tenant',
          required: true,
          props: {
            showSearch: true,
          }
        },
        {
          id: 'grnDate',
          type: 'date',
          label: 'GRN Date',
          required: true,
          validation: [
            { type: 'required', message: 'GRN Date is required' }
          ]
        },
        {
          id: 'sellerId',
          type: 'custom',
          component: 'sellerSelector',
          label: 'Vendor',
          required: true,
          dependencies: [
            {
              field: 'selectedTenant',
              condition: 'hasValue',
              action: 'enable'
            }
          ]
        }
      ]
    },
    {
      id: 'address-info',
      title: 'Address Information',
      fields: [
        {
          id: 'vendorAddress',
          type: 'custom',
          component: 'addressSelector',
          label: 'Vendor Address',
          props: {
            title: 'Vendor Address',
            addressType: 'VENDOR'
          },
          dependencies: [
            {
              field: 'sellerId',
              condition: 'hasValue',
              action: 'enable'
            }
          ]
        }
      ]
    }
  ]
};
```

### 2.2 Create PO Form Schema
```typescript
// src/schemas/POFormSchema.ts
export const POFormSchema: FormSchema = {
  id: 'po-form',
  title: 'Purchase Order',
  sections: [
    {
      id: 'basic-info',
      title: 'Basic Information',
      layout: { columns: 2 },
      fields: [
        {
          id: 'poNumber',
          type: 'custom',
          component: 'documentSequence',
          label: 'PO Number',
          required: true,
          props: {
            entityName: 'PURCHASE_ORDER',
            docTitle: 'Purchase Order#',
          }
        },
        {
          id: 'selectedTenant',
          type: 'custom',
          component: 'tenantSelector',
          label: 'Tenant',
          required: true,
        },
        {
          id: 'poDate',
          type: 'date',
          label: 'PO Date',
          required: true,
        },
        {
          id: 'sellerId',
          type: 'custom',
          component: 'sellerSelector',
          label: 'Vendor',
          required: true,
          dependencies: [
            {
              field: 'selectedTenant',
              condition: 'hasValue',
              action: 'enable'
            }
          ]
        }
      ]
    }
  ]
};
```

## Phase 3: Form Renderer Component (Week 3-4)

### 3.1 Create Main Form Renderer
```typescript
// src/components/Common/FormRenderer/FormRenderer.tsx
export const FormRenderer: React.FC<FormRendererProps> = ({
  schema,
  initialData,
  onSubmit,
  loading,
  className
}) => {
  const {
    formData,
    errors,
    updateField,
    validateForm,
    renderField
  } = useFormRenderer(schema, initialData);

  const handleSubmit = async () => {
    const isValid = validateForm();
    if (isValid) {
      await onSubmit(formData);
    }
  };

  return (
    <div className={`form-renderer ${className}`}>
      {schema.sections.map(section => (
        <FormSection
          key={section.id}
          section={section}
          formData={formData}
          errors={errors}
          updateField={updateField}
          renderField={renderField}
        />
      ))}
      <FormActions onSubmit={handleSubmit} loading={loading} />
    </div>
  );
};
```

## Phase 4: Migration Strategy (Week 4-6)

### 4.1 Start with Simple Forms
1. **Settings forms** (simpler, fewer dependencies)
2. **Basic CRUD forms** (Create/Update User, Department, etc.)
3. **Move to complex forms** (GRN, PO, Invoice)

### 4.2 Gradual Migration Approach
```typescript
// Hybrid approach - use both old and new side by side
const GRNFormV3 = () => {
  const [useNewRenderer, setUseNewRenderer] = useState(false);
  
  if (useNewRenderer) {
    return <FormRenderer schema={GRNFormSchema} onSubmit={handleSubmit} />;
  }
  
  return <GRNFormV2 />; // Existing implementation
};
```

## Phase 5: Advanced Features (Week 6-8)

### 5.1 Dynamic Schema Loading
```typescript
// Load schemas from API or config
const useFormSchema = (formType: string) => {
  const [schema, setSchema] = useState(null);
  
  useEffect(() => {
    // Load schema from API or import dynamically
    import(`../schemas/${formType}Schema`).then(setSchema);
  }, [formType]);
  
  return schema;
};
```

### 5.2 Schema Builder UI (Future)
- Visual form builder for non-developers
- Drag-and-drop field arrangement
- Real-time preview

## Benefits After Implementation

1. **Code Reduction**: 70-80% less form code
2. **Consistency**: All forms follow same patterns
3. **Maintainability**: Single place to update form logic
4. **Type Safety**: Schema-driven TypeScript support
5. **Testing**: Test schemas instead of individual forms
6. **Performance**: Optimized rendering and validation

## Recommended Next Steps

1. **Start with Phase 1** - Create basic schema types and hook
2. **Pick a simple form** - Maybe Settings/User form for proof of concept
3. **Build incrementally** - Add one field type at a time
4. **Get team feedback** - Iterate on the API design
5. **Document patterns** - Create guidelines for schema creation

This approach will transform your form development from repetitive coding to declarative schema definition!
