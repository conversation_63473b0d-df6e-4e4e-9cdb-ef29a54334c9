import React from 'react';
import { DatePicker, Checkbox, Radio } from 'antd';

// Import your existing form components
import H3FormInput from '@Uilib/h3FormInput';
import PRZSelect from '../UI/PRZSelect';
import DocumentNumberSeqInput from '../../Admin/Common/DocumentNumberSeqInput';
import SelectSellerV2 from '../SelectSellerV2';
import AddressSelector from '../FormUtils/AddressSelecter';
import TenantSelector from '../Selector/TenantSelector';
import SelectPaymentTerm from '../SelectPaymentTerm';
import SelectDepartment from '../SelectDepartment';
import TagSelector from '../Selector/TagSelector';
import CustomFieldV3 from '../CustomFieldV3';

// Simplified types for the useForm hook approach
export interface FormFieldConfig {
  id: string;
  type: 'text' | 'email' | 'number' | 'select' | 'date' | 'checkbox' | 'custom';
  label: string;
  required?: boolean;
  disabled?: boolean;
  placeholder?: string;
  validation?: ValidationRule[];
  options?: SelectOption[]; // For select fields
  component?: string; // For custom components
  props?: Record<string, any>; // Additional props for custom components
}

export interface ValidationRule {
  type: 'required' | 'email' | 'min' | 'max' | 'minLength' | 'maxLength' | 'pattern' | 'custom';
  value?: any;
  message: string;
  validator?: (value: any, formData: any) => boolean | string;
}

export interface SelectOption {
  label: string;
  value: any;
  disabled?: boolean;
}

export interface FieldRenderProps {
  field: FormFieldConfig;
  value: any;
  error?: string;
  onChange: (value: any) => void;
  disabled?: boolean;
  formData: Record<string, any>;
}

// Base field components for standard types
const TextField: React.FC<FieldRendererProps> = ({
  field,
  value,
  error,
  onChange,
  disabled,
  required,
  readOnly
}) => {
  const textField = field as any; // Type assertion for specific field properties

  return (
    <H3FormInput
      name={field.label}
      type={field.type === 'textarea' ? 'textarea' : field.type}
      value={value || ''}
      placeholder={field.placeholder}
      disabled={disabled || readOnly}
      required={required}
      maxLength={textField.maxLength}
      rows={textField.rows}
      containerClassName={`${field.containerClassName || ''} ${error ? 'form-error__input' : ''}`}
      labelClassName={field.labelClassName}
      inputClassName={field.inputClassName}
      onChange={(e) => onChange(e.target.value)}
    />
  );
};

const NumberField: React.FC<FieldRendererProps> = ({
  field,
  value,
  error,
  onChange,
  disabled,
  required,
  readOnly
}) => {
  const numberField = field as any;

  return (
    <H3FormInput
      name={field.label}
      type="number"
      value={value || ''}
      placeholder={field.placeholder}
      disabled={disabled || readOnly}
      required={required}
      min={numberField.min}
      max={numberField.max}
      step={numberField.step}
      containerClassName={`${field.containerClassName || ''} ${error ? 'form-error__input' : ''}`}
      labelClassName={field.labelClassName}
      inputClassName={field.inputClassName}
      onChange={(e) => onChange(e.target.value)}
    />
  );
};

const SelectField: React.FC<FieldRendererProps> = ({
  field,
  value,
  error,
  onChange,
  disabled,
  required,
  readOnly
}) => {
  const selectField = field as any;

  return (
    <PRZSelect
      label={field.label}
      value={value}
      placeholder={field.placeholder}
      disabled={disabled || readOnly}
      required={required}
      allowClear={selectField.allowClear}
      showSearch={selectField.searchable}
      mode={selectField.mode}
      className={`${field.containerClassName || ''} ${error ? 'form-error__input' : ''}`}
      onChange={onChange}
    >
      {(selectField.options || []).map((option: SelectOption) => (
        <PRZSelect.Option
          key={option.value}
          value={option.value}
          disabled={option.disabled}
        >
          {option.label}
        </PRZSelect.Option>
      ))}
    </PRZSelect>
  );
};

const DateField: React.FC<FieldRendererProps> = ({
  field,
  value,
  error,
  onChange,
  disabled,
  required,
  readOnly
}) => {
  const dateField = field as any;

  return (
    <div className={`form-field ${field.containerClassName || ''} ${error ? 'form-error__input' : ''}`}>
      <label className={field.labelClassName}>
        {field.label}
        {required && <span className="required-asterisk">*</span>}
      </label>
      <DatePicker
        value={value}
        placeholder={field.placeholder}
        disabled={disabled || readOnly}
        format={dateField.format || 'DD/MM/YYYY'}
        showTime={dateField.showTime}
        disabledDate={dateField.disabledDate}
        className={field.inputClassName}
        onChange={onChange}
        style={{ width: '100%' }}
      />
    </div>
  );
};

const CheckboxField: React.FC<FieldRendererProps> = ({
  field,
  value,
  error,
  onChange,
  disabled,
  required,
  readOnly
}) => {
  const checkboxField = field as any;

  return (
    <div className={`form-field ${field.containerClassName || ''} ${error ? 'form-error__input' : ''}`}>
      <Checkbox
        checked={value || false}
        disabled={disabled || readOnly}
        className={field.inputClassName}
        onChange={(e) => onChange(e.target.checked)}
      >
        {checkboxField.checkboxText || field.label}
      </Checkbox>
    </div>
  );
};

const RadioField: React.FC<FieldRendererProps> = ({
  field,
  value,
  error,
  onChange,
  disabled,
  required,
  readOnly
}) => {
  const radioField = field as any;

  return (
    <div className={`form-field ${field.containerClassName || ''} ${error ? 'form-error__input' : ''}`}>
      <label className={field.labelClassName}>
        {field.label}
        {required && <span className="required-asterisk">*</span>}
      </label>
      <Radio.Group
        value={value}
        disabled={disabled || readOnly}
        className={field.inputClassName}
        onChange={(e) => onChange(e.target.value)}
      >
        {(radioField.options || []).map((option: SelectOption) => (
          <Radio
            key={option.value}
            value={option.value}
            disabled={option.disabled}
            style={{
              display: radioField.direction === 'vertical' ? 'block' : 'inline-block',
              marginBottom: radioField.direction === 'vertical' ? '8px' : '0'
            }}
          >
            {option.label}
          </Radio>
        ))}
      </Radio.Group>
    </div>
  );
};

// Custom component wrappers for your existing complex components
const DocumentSequenceField: React.FC<FieldRendererProps> = ({
  field,
  value,
  error,
  onChange,
  disabled,
  formData,
  readOnly
}) => {
  const customField = field as any;

  return (
    <DocumentNumberSeqInput
      valueFromProps={value}
      updateCase={customField.props?.updateCase}
      setInitialDocSeqNumber={customField.props?.setInitialDocSeqNumber}
      entityName={customField.props?.entityName}
      docSeqId={customField.props?.docSeqId}
      tenantId={customField.props?.tenantId || formData?.selectedTenant}
      onChangeFromProps={(event, newValue, seqId) => {
        onChange(newValue || event?.target?.value || '');
        // Handle additional props if needed
        if (customField.props?.onDocSeqChange) {
          customField.props.onDocSeqChange(seqId);
        }
      }}
      docTitle={customField.props?.docTitle}
      formSubmitted={!!error}
      disabled={disabled || readOnly}
      {...customField.props}
    />
  );
};

const SellerSelectorField: React.FC<FieldRendererProps> = ({
  field,
  value,
  error,
  onChange,
  disabled,
  formData,
  readOnly
}) => {
  const customField = field as any;

  return (
    <SelectSellerV2
      selectedSeller={value}
      onChange={onChange}
      disabled={disabled || readOnly}
      tenantId={formData?.selectedTenant}
      {...customField.props}
    />
  );
};

const AddressSelectorField: React.FC<FieldRendererProps> = ({
  field,
  value,
  error,
  onChange,
  disabled,
  formData,
  readOnly
}) => {
  const customField = field as any;

  return (
    <AddressSelector
      title={customField.props?.title || field.label}
      addressType={customField.props?.addressType}
      selectedAddressId={value?.address_id}
      onChange={onChange}
      disabled={disabled || readOnly}
      {...customField.props}
    />
  );
};

const TenantSelectorField: React.FC<FieldRendererProps> = ({
  field,
  value,
  error,
  onChange,
  disabled,
  readOnly
}) => {
  const customField = field as any;

  return (
    <TenantSelector
      selectedTenant={value}
      onChange={onChange}
      disabled={disabled || readOnly}
      showSearch={customField.props?.showSearch}
      {...customField.props}
    />
  );
};

// Registry mapping field types and custom components to their renderers
export const FIELD_COMPONENTS: Record<string, React.FC<FieldRendererProps>> = {
  // Standard field types
  text: TextField,
  email: TextField,
  password: TextField,
  textarea: TextField,
  number: NumberField,
  currency: NumberField,
  select: SelectField,
  multiselect: SelectField,
  date: DateField,
  datetime: DateField,
  checkbox: CheckboxField,
  radio: RadioField,

  // Custom components (mapped by component name)
  documentSequence: DocumentSequenceField,
  sellerSelector: SellerSelectorField,
  addressSelector: AddressSelectorField,
  tenantSelector: TenantSelectorField,

  // Add more custom components as needed
  // paymentTermSelector: PaymentTermSelectorField,
  // departmentSelector: DepartmentSelectorField,
  // tagSelector: TagSelectorField,
  // customField: CustomFieldField,
};

// Helper function to get the appropriate component for a field
export const getFieldComponent = (field: any): React.FC<FieldRendererProps> => {
  if (field.type === 'custom') {
    return FIELD_COMPONENTS[field.component] || TextField;
  }

  return FIELD_COMPONENTS[field.type] || TextField;
};

// Helper function to register new custom components
export const registerCustomComponent = (
  name: string,
  component: React.FC<FieldRendererProps>
) => {
  FIELD_COMPONENTS[name] = component;
};
