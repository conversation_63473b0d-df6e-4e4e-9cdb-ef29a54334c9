import React from 'react';
import { FieldRendererProps } from '../../../types/FormSchema';
import { getFieldComponent } from './FieldRegistry';

const FieldRenderer: React.FC<FieldRendererProps> = (props) => {
  const { field, error } = props;
  
  // Get the appropriate component for this field type
  const FieldComponent = getFieldComponent(field);
  
  return (
    <div className={`form-field-wrapper ${field.layout?.span ? `col-span-${field.layout.span}` : ''}`}>
      <FieldComponent {...props} />
      {error && (
        <div className="form-field-error">
          {error}
        </div>
      )}
      {field.helpText && (
        <div className="form-field-help">
          {field.helpText}
        </div>
      )}
    </div>
  );
};

export default FieldRenderer;
