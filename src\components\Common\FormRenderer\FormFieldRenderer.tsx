import React from 'react';
import { DatePicker } from 'antd';
import dayjs from 'dayjs';

// Import your existing components
import H3FormInput from '@Uilib/h3FormInput';
import PRZSelect from '../UI/PRZSelect';
import DocumentNumberSeqInput from '../../Admin/Common/DocumentNumberSeqInput';
import SelectSellerV2 from '../SelectSellerV2';
import AddressSelector from '../FormUtils/AddressSelecter';
import TenantSelector from '../Selector/TenantSelector';
import SelectPaymentTerm from '../SelectPaymentTerm';
import SelectDepartment from '../SelectDepartment';

import { FormFieldConfig, FieldProps } from '../../../hooks/useForm';

// Extended field config for rendering
export interface RenderableFieldConfig extends FormFieldConfig {
  type: 'text' | 'email' | 'number' | 'textarea' | 'select' | 'date' | 'custom';
  placeholder?: string;
  options?: Array<{ label: string; value: any; disabled?: boolean }>;
  component?: string; // For custom components
  props?: Record<string, any>; // Additional props for custom components
  rows?: number; // For textarea
  showTime?: boolean; // For date fields
  format?: string; // For date fields
}

interface FormFieldRendererProps {
  fieldConfig: RenderableFieldConfig;
  fieldProps: FieldProps;
  formData?: Record<string, any>; // For custom components that need access to other form data
}

const FormFieldRenderer: React.FC<FormFieldRendererProps> = ({
  fieldConfig,
  fieldProps,
  formData = {}
}) => {
  const { type, placeholder, options, component, props: customProps, rows, showTime, format } = fieldConfig;
  const { value, onChange, error, required, disabled, label, containerClassName } = fieldProps;

  // Handle different field types
  switch (type) {
    case 'text':
    case 'email':
      return (
        <H3FormInput
          name={label}
          type={type}
          value={value}
          placeholder={placeholder}
          disabled={disabled}
          required={required}
          containerClassName={containerClassName}
          onChange={(e) => onChange(e.target.value)}
        />
      );

    case 'number':
      return (
        <H3FormInput
          name={label}
          type="number"
          value={value}
          placeholder={placeholder}
          disabled={disabled}
          required={required}
          containerClassName={containerClassName}
          onChange={(e) => onChange(e.target.value)}
        />
      );

    case 'textarea':
      return (
        <H3FormInput
          name={label}
          type="textarea"
          value={value}
          placeholder={placeholder}
          disabled={disabled}
          required={required}
          rows={rows || 3}
          containerClassName={containerClassName}
          onChange={(e) => onChange(e.target.value)}
        />
      );

    case 'select':
      return (
        <PRZSelect
          label={label}
          value={value}
          placeholder={placeholder}
          disabled={disabled}
          required={required}
          className={containerClassName}
          onChange={onChange}
        >
          {(options || []).map((option) => (
            <PRZSelect.Option 
              key={option.value} 
              value={option.value}
              disabled={option.disabled}
            >
              {option.label}
            </PRZSelect.Option>
          ))}
        </PRZSelect>
      );

    case 'date':
      return (
        <div className={`form-field ${containerClassName}`}>
          <label>
            {label}
            {required && <span style={{ color: 'red' }}>*</span>}
          </label>
          <DatePicker
            value={value ? dayjs(value) : null}
            placeholder={placeholder}
            disabled={disabled}
            format={format || 'DD/MM/YYYY'}
            showTime={showTime}
            style={{ width: '100%' }}
            onChange={(date) => onChange(date ? date.toDate() : null)}
          />
        </div>
      );

    case 'custom':
      return renderCustomComponent(component, fieldProps, customProps, formData);

    default:
      return (
        <H3FormInput
          name={label}
          type="text"
          value={value}
          placeholder={placeholder}
          disabled={disabled}
          required={required}
          containerClassName={containerClassName}
          onChange={(e) => onChange(e.target.value)}
        />
      );
  }
};

// Helper function to render custom components
const renderCustomComponent = (
  component: string | undefined,
  fieldProps: FieldProps,
  customProps: Record<string, any> = {},
  formData: Record<string, any> = {}
) => {
  const { value, onChange, error, required, disabled, label } = fieldProps;

  switch (component) {
    case 'documentSequence':
      return (
        <DocumentNumberSeqInput
          valueFromProps={value}
          updateCase={customProps?.updateCase}
          setInitialDocSeqNumber={customProps?.setInitialDocSeqNumber}
          entityName={customProps?.entityName}
          docSeqId={customProps?.docSeqId}
          tenantId={customProps?.tenantId || formData?.selectedTenant}
          onChangeFromProps={(event, newValue, seqId) => {
            onChange(newValue || event?.target?.value || '');
            if (customProps?.onDocSeqChange) {
              customProps.onDocSeqChange(seqId);
            }
          }}
          docTitle={customProps?.docTitle || label}
          formSubmitted={!!error}
          disabled={disabled}
          {...customProps}
        />
      );

    case 'sellerSelector':
      return (
        <SelectSellerV2
          selectedSeller={value}
          onChange={onChange}
          disabled={disabled}
          tenantId={formData?.selectedTenant}
          {...customProps}
        />
      );

    case 'addressSelector':
      return (
        <AddressSelector
          title={customProps?.title || label}
          addressType={customProps?.addressType}
          selectedAddressId={value?.address_id}
          onChange={onChange}
          disabled={disabled}
          {...customProps}
        />
      );

    case 'tenantSelector':
      return (
        <TenantSelector
          selectedTenant={value}
          onChange={onChange}
          disabled={disabled}
          showSearch={customProps?.showSearch}
          {...customProps}
        />
      );

    case 'paymentTermSelector':
      return (
        <SelectPaymentTerm
          selectedPaymentTerm={value}
          onChange={onChange}
          disabled={disabled}
          {...customProps}
        />
      );

    case 'departmentSelector':
      return (
        <SelectDepartment
          selectedDepartment={value}
          onChange={onChange}
          disabled={disabled}
          {...customProps}
        />
      );

    default:
      // Fallback to text input for unknown custom components
      return (
        <H3FormInput
          name={label}
          type="text"
          value={value}
          disabled={disabled}
          required={required}
          containerClassName={error ? 'form-error__input' : ''}
          onChange={(e) => onChange(e.target.value)}
        />
      );
  }
};

export default FormFieldRenderer;

// Helper function to create field configs easily
export const createFieldConfig = (
  id: string,
  label: string,
  type: RenderableFieldConfig['type'],
  options: Partial<RenderableFieldConfig> = {}
): RenderableFieldConfig => ({
  id,
  label,
  type,
  ...options
});

// Common field config creators
export const textField = (id: string, label: string, options: Partial<RenderableFieldConfig> = {}) =>
  createFieldConfig(id, label, 'text', options);

export const emailField = (id: string, label: string, options: Partial<RenderableFieldConfig> = {}) =>
  createFieldConfig(id, label, 'email', options);

export const numberField = (id: string, label: string, options: Partial<RenderableFieldConfig> = {}) =>
  createFieldConfig(id, label, 'number', options);

export const selectField = (id: string, label: string, selectOptions: Array<{ label: string; value: any }>, options: Partial<RenderableFieldConfig> = {}) =>
  createFieldConfig(id, label, 'select', { ...options, options: selectOptions });

export const dateField = (id: string, label: string, options: Partial<RenderableFieldConfig> = {}) =>
  createFieldConfig(id, label, 'date', options);

export const customField = (id: string, label: string, component: string, props: Record<string, any> = {}, options: Partial<RenderableFieldConfig> = {}) =>
  createFieldConfig(id, label, 'custom', { ...options, component, props });
