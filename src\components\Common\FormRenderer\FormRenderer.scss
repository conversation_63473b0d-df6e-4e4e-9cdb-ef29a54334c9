.form-renderer {
  .form-renderer-form {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .form-header {
    margin-bottom: 24px;
    
    .form-title {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: #262626;
    }
    
    .form-description {
      margin: 0;
      color: #8c8c8c;
      font-size: 14px;
    }
  }

  .form-content {
    display: flex;
    flex-direction: column;
    gap: 32px;
  }

  .form-section {
    .form-section-header {
      margin-bottom: 16px;
      
      .form-section-title {
        margin: 0 0 4px 0;
        font-size: 18px;
        font-weight: 500;
        color: #262626;
      }
      
      .form-section-description {
        margin: 0;
        color: #8c8c8c;
        font-size: 13px;
      }
    }

    .form-section-grid {
      display: grid;
      gap: 16px;
      
      &.grid-cols-1 {
        grid-template-columns: 1fr;
      }
      
      &.grid-cols-2 {
        grid-template-columns: 1fr 1fr;
      }
      
      &.grid-cols-3 {
        grid-template-columns: 1fr 1fr 1fr;
      }
      
      &.grid-cols-4 {
        grid-template-columns: 1fr 1fr 1fr 1fr;
      }

      @media (max-width: 768px) {
        grid-template-columns: 1fr !important;
      }
    }
  }

  .form-field-wrapper {
    display: flex;
    flex-direction: column;
    
    .form-field {
      display: flex;
      flex-direction: column;
      gap: 4px;
      
      label {
        font-weight: 500;
        color: #262626;
        font-size: 14px;
        
        .required-asterisk {
          color: #ff4d4f;
          margin-left: 4px;
        }
      }
    }
    
    .form-field-error {
      color: #ff4d4f;
      font-size: 12px;
      margin-top: 4px;
    }
    
    .form-field-help {
      color: #8c8c8c;
      font-size: 12px;
      margin-top: 4px;
    }

    // Grid span classes
    &.col-span-1 { grid-column: span 1; }
    &.col-span-2 { grid-column: span 2; }
    &.col-span-3 { grid-column: span 3; }
    &.col-span-4 { grid-column: span 4; }
    &.col-span-5 { grid-column: span 5; }
    &.col-span-6 { grid-column: span 6; }
    &.col-span-7 { grid-column: span 7; }
    &.col-span-8 { grid-column: span 8; }
    &.col-span-9 { grid-column: span 9; }
    &.col-span-10 { grid-column: span 10; }
    &.col-span-11 { grid-column: span 11; }
    &.col-span-12 { grid-column: span 12; }
  }

  .form-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    padding-top: 24px;
    border-top: 1px solid #f0f0f0;
    margin-top: 32px;

    @media (max-width: 768px) {
      flex-direction: column;
      
      .ant-btn {
        width: 100%;
      }
    }
  }

  // Error state styling
  .form-error__input {
    .ant-input,
    .ant-select-selector,
    .ant-picker {
      border-color: #ff4d4f !important;
      box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2) !important;
    }
  }

  // Loading state
  .ant-spin-nested-loading {
    .ant-spin-container {
      transition: opacity 0.3s;
    }
  }

  // Responsive adjustments
  @media (max-width: 768px) {
    .form-header {
      .form-title {
        font-size: 20px;
      }
    }
    
    .form-section {
      .form-section-title {
        font-size: 16px;
      }
    }
  }
}

// Collapsible section styling
.form-section {
  .ant-collapse {
    background: transparent;
    border: none;
    
    .ant-collapse-item {
      border: 1px solid #f0f0f0;
      border-radius: 6px;
      margin-bottom: 16px;
      
      .ant-collapse-header {
        padding: 16px 20px;
        background: #fafafa;
        border-radius: 6px 6px 0 0;
        
        .form-section-header {
          margin: 0;
          
          .form-section-title {
            margin: 0;
            font-size: 16px;
          }
          
          .form-section-description {
            margin: 4px 0 0 0;
          }
        }
      }
      
      .ant-collapse-content {
        border-top: 1px solid #f0f0f0;
        
        .ant-collapse-content-box {
          padding: 20px;
        }
      }
      
      &.ant-collapse-item-active {
        .ant-collapse-header {
          border-radius: 6px 6px 0 0;
        }
      }
    }
  }
}
