import React from 'react';
import { <PERSON><PERSON>, <PERSON> } from 'antd';
import { FormRendererProps } from '../../../types/FormSchema';
import { useFormRenderer } from '../../../hooks/useFormRenderer';
import FormSection from './FormSection';
import './FormRenderer.scss';

const FormRenderer: React.FC<FormRendererProps> = ({
  schema,
  initialData = {},
  onSubmit,
  onChange,
  loading: externalLoading = false,
  className = '',
  readOnly = false,
}) => {
  const {
    formData,
    errors,
    touched,
    loading: internalLoading,
    updateField,
    validateForm,
    resetForm,
  } = useFormRenderer(schema, initialData);

  const isLoading = externalLoading || internalLoading;

  // Trigger onChange callback when form data changes
  React.useEffect(() => {
    if (onChange) {
      onChange(formData);
    }
  }, [formData, onChange]);

  const handleSubmit = async (e?: React.FormEvent) => {
    if (e) {
      e.preventDefault();
    }

    if (readOnly) return;

    // Validate form before submission
    const isValid = validateForm();
    if (!isValid) {
      console.log('Form validation failed:', errors);
      return;
    }

    // Run schema-level validation if configured
    if (schema.validation?.onSubmit) {
      const validationResult = await schema.validation.onSubmit(formData);
      if (validationResult !== true) {
        console.log('Schema validation failed:', validationResult);
        return;
      }
    }

    try {
      await onSubmit(formData);
      
      // Reset form if configured
      if (schema.config?.resetOnSubmit) {
        resetForm();
      }
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  const handleReset = () => {
    if (schema.actions?.reset?.onClick) {
      schema.actions.reset.onClick();
    } else {
      resetForm();
    }
  };

  const handleCancel = () => {
    if (schema.actions?.cancel?.onClick) {
      schema.actions.cancel.onClick();
    }
  };

  return (
    <div className={`form-renderer ${className}`}>
      <Spin spinning={isLoading}>
        <form onSubmit={handleSubmit} className="form-renderer-form">
          {/* Form Header */}
          {(schema.title || schema.description) && (
            <div className="form-header">
              {schema.title && (
                <h2 className="form-title">{schema.title}</h2>
              )}
              {schema.description && (
                <p className="form-description">{schema.description}</p>
              )}
            </div>
          )}

          {/* Form Sections */}
          <div className="form-content">
            {schema.sections.map(section => (
              <FormSection
                key={section.id}
                section={section}
                formData={formData}
                errors={errors}
                touched={touched}
                updateField={updateField}
                isFieldVisible={(fieldId) => {
                  // This would be implemented in the hook
                  return true; // Simplified for now
                }}
                isFieldDisabled={(fieldId) => {
                  // This would be implemented in the hook
                  return false; // Simplified for now
                }}
                isFieldRequired={(fieldId) => {
                  const field = section.fields.find(f => f.id === fieldId);
                  return field?.required || false;
                }}
                readOnly={readOnly}
              />
            ))}
          </div>

          {/* Form Actions */}
          {!readOnly && (
            <div className="form-actions">
              {/* Submit Button */}
              {schema.actions?.submit !== false && (
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={isLoading}
                  disabled={schema.actions?.submit?.disabled}
                  className={schema.actions?.submit?.className}
                >
                  {schema.actions?.submit?.text || 'Submit'}
                </Button>
              )}

              {/* Cancel Button */}
              {schema.actions?.cancel && (
                <Button
                  type="default"
                  onClick={handleCancel}
                  className={schema.actions.cancel.className}
                >
                  {schema.actions.cancel.text || 'Cancel'}
                </Button>
              )}

              {/* Reset Button */}
              {schema.actions?.reset && (
                <Button
                  type="default"
                  onClick={handleReset}
                  className={schema.actions.reset.className}
                >
                  {schema.actions.reset.text || 'Reset'}
                </Button>
              )}

              {/* Custom Actions */}
              {schema.actions?.custom?.map((action, index) => (
                <Button
                  key={index}
                  type={action.type || 'default'}
                  onClick={() => action.onClick(formData)}
                  className={action.className}
                >
                  {action.text}
                </Button>
              ))}
            </div>
          )}
        </form>
      </Spin>
    </div>
  );
};

export default FormRenderer;
