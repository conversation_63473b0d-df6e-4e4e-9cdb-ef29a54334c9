import React, { useState } from 'react';
import { Card, Button, Space, message } from 'antd';
import FormRenderer from './FormRenderer';
import { SimpleFormSchema, GRNFormSchemaExample } from '../../../schemas/SimpleFormSchema';

const FormRendererTest: React.FC = () => {
  const [currentSchema, setCurrentSchema] = useState(SimpleFormSchema);
  const [submittedData, setSubmittedData] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (formData: Record<string, any>) => {
    setLoading(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    console.log('Form submitted with data:', formData);
    setSubmittedData(formData);
    message.success('Form submitted successfully!');
    
    setLoading(false);
  };

  const handleFormChange = (formData: Record<string, any>) => {
    console.log('Form data changed:', formData);
  };

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <h1>Form Renderer Test</h1>
      
      <Space style={{ marginBottom: '24px' }}>
        <Button 
          type={currentSchema.id === 'simple-form' ? 'primary' : 'default'}
          onClick={() => setCurrentSchema(SimpleFormSchema)}
        >
          Simple Form
        </Button>
        <Button 
          type={currentSchema.id === 'grn-form-example' ? 'primary' : 'default'}
          onClick={() => setCurrentSchema(GRNFormSchemaExample)}
        >
          GRN Form Example
        </Button>
      </Space>

      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '24px' }}>
        {/* Form Renderer */}
        <Card title="Form Renderer" style={{ height: 'fit-content' }}>
          <FormRenderer
            schema={currentSchema}
            onSubmit={handleSubmit}
            onChange={handleFormChange}
            loading={loading}
            initialData={{
              firstName: 'John',
              email: '<EMAIL>'
            }}
          />
        </Card>

        {/* Debug Panel */}
        <Card title="Debug Information" style={{ height: 'fit-content' }}>
          <div>
            <h4>Current Schema:</h4>
            <pre style={{ 
              background: '#f5f5f5', 
              padding: '12px', 
              borderRadius: '4px',
              fontSize: '12px',
              overflow: 'auto',
              maxHeight: '200px'
            }}>
              {JSON.stringify(currentSchema, null, 2)}
            </pre>
          </div>

          {submittedData && (
            <div style={{ marginTop: '16px' }}>
              <h4>Last Submitted Data:</h4>
              <pre style={{ 
                background: '#f0f9ff', 
                padding: '12px', 
                borderRadius: '4px',
                fontSize: '12px',
                overflow: 'auto',
                maxHeight: '200px'
              }}>
                {JSON.stringify(submittedData, null, 2)}
              </pre>
            </div>
          )}
        </Card>
      </div>

      {/* Usage Instructions */}
      <Card title="Usage Instructions" style={{ marginTop: '24px' }}>
        <div>
          <h4>How to use the Form Renderer:</h4>
          <ol>
            <li>Define your form schema using the <code>FormSchema</code> interface</li>
            <li>Import and use the <code>FormRenderer</code> component</li>
            <li>Pass your schema and handlers to the component</li>
            <li>The form will automatically render fields, handle validation, and manage state</li>
          </ol>

          <h4>Example usage:</h4>
          <pre style={{ 
            background: '#f5f5f5', 
            padding: '12px', 
            borderRadius: '4px',
            fontSize: '12px'
          }}>
{`import { FormRenderer } from '@Components/Common/FormRenderer';
import { MyFormSchema } from './schemas/MyFormSchema';

const MyForm = () => {
  const handleSubmit = async (formData) => {
    // Handle form submission
    console.log('Form data:', formData);
  };

  return (
    <FormRenderer
      schema={MyFormSchema}
      onSubmit={handleSubmit}
      initialData={{ field1: 'default value' }}
    />
  );
};`}
          </pre>
        </div>
      </Card>
    </div>
  );
};

export default FormRendererTest;
