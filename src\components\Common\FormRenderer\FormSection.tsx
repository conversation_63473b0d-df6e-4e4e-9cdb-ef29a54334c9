import React, { useState } from 'react';
import { Collapse } from 'antd';
import { CaretRightOutlined } from '@ant-design/icons';
import { FormSectionProps } from '../../../types/FormSchema';
import FieldRenderer from './FieldRenderer';

const { Panel } = Collapse;

const FormSection: React.FC<FormSectionProps> = ({
  section,
  formData,
  errors,
  touched,
  updateField,
  isFieldVisible,
  isFieldDisabled,
  isFieldRequired,
  readOnly = false,
}) => {
  const [collapsed, setCollapsed] = useState(section.defaultCollapsed || false);

  // Check if section should be visible based on dependencies
  const isSectionVisible = () => {
    if (section.visible === false) return false;
    
    if (!section.dependencies) return true;

    return section.dependencies.every(dep => {
      const depValue = formData[dep.field];
      let conditionMet = false;

      switch (dep.condition) {
        case 'hasValue':
          conditionMet = depValue !== undefined && depValue !== null && depValue !== '';
          break;
        case 'equals':
          conditionMet = depValue === dep.value;
          break;
        case 'notEquals':
          conditionMet = depValue !== dep.value;
          break;
        case 'custom':
          conditionMet = dep.customCondition ? dep.customCondition(depValue, formData) : true;
          break;
      }

      return conditionMet && (dep.action === 'show' || dep.action === 'enable');
    });
  };

  if (!isSectionVisible()) {
    return null;
  }

  // Filter visible fields
  const visibleFields = section.fields.filter(field => isFieldVisible(field.id));

  // Determine grid layout
  const gridColumns = section.layout?.columns || 1;
  const gridClass = `form-section-grid grid-cols-${gridColumns}`;

  const renderFields = () => (
    <div className={`${gridClass} ${section.layout?.className || ''}`}>
      {visibleFields.map(field => (
        <FieldRenderer
          key={field.id}
          field={field}
          value={formData[field.id]}
          error={errors[field.id]}
          touched={touched[field.id]}
          onChange={(value) => updateField(field.id, value)}
          disabled={isFieldDisabled(field.id)}
          required={isFieldRequired(field.id)}
          formData={formData}
          readOnly={readOnly}
        />
      ))}
    </div>
  );

  // If section is collapsible, wrap in Collapse component
  if (section.collapsible) {
    return (
      <div className={`form-section ${section.className || ''}`}>
        <Collapse
          activeKey={collapsed ? [] : [section.id]}
          onChange={() => setCollapsed(!collapsed)}
          expandIcon={({ isActive }) => (
            <CaretRightOutlined rotate={isActive ? 90 : 0} />
          )}
          ghost
        >
          <Panel
            header={
              <div className="form-section-header">
                <h3 className="form-section-title">{section.title}</h3>
                {section.description && (
                  <p className="form-section-description">{section.description}</p>
                )}
              </div>
            }
            key={section.id}
          >
            {renderFields()}
          </Panel>
        </Collapse>
      </div>
    );
  }

  // Regular section without collapse
  return (
    <div className={`form-section ${section.className || ''}`}>
      {(section.title || section.description) && (
        <div className="form-section-header">
          {section.title && (
            <h3 className="form-section-title">{section.title}</h3>
          )}
          {section.description && (
            <p className="form-section-description">{section.description}</p>
          )}
        </div>
      )}
      {renderFields()}
    </div>
  );
};

export default FormSection;
