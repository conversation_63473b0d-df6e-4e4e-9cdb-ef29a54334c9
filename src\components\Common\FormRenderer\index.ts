// Main exports for the useForm hook approach
export { default as <PERSON><PERSON>ieldRenderer } from './FormFieldRenderer';
export {
  textField,
  emailField,
  numberField,
  selectField,
  dateField,
  customField,
  createFieldConfig
} from './FormFieldRenderer';

// Hook export
export { useForm } from '../../../hooks/useForm';

// Type exports
export type {
  FormFieldConfig,
  ValidationRule,
  UseFormReturn,
  FieldProps
} from '../../../hooks/useForm';

export type {
  RenderableFieldConfig
} from './FormFieldRenderer';
