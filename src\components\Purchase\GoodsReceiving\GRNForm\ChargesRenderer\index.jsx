import React, { memo } from 'react';

// Icons
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCircleXmark } from '@fortawesome/free-solid-svg-icons';

// Helpers
import Helpers from '@Apis/helpers';

// Components
import H3FormInput from '@Uilib/h3FormInput';
import SelectExtraCharge from '@Components/Admin/Common/SelectExtraCharge';
import ChargesTaxInput from '@Components/Common/ChargesTaxInput';

// Local Imports
import { ACTIONS } from '../reducer';

const ChargesRenderer = ({
  chargeData,
  data,
  charge1Value,
  createGRNLoading,
  updateGRNLoading,
  user,
  selectedSeller,
  selectedPoForGrn,
  selectedTenant,
  isVendorOverseas,
  formSubmitted,
  localDispatch,
  updateLandedCost,
  getBillFromAddress,
  getBillToAddress,
  handleDeleteCharge,
}) => {

  const handleChargeChange = (value, chargeKey, field) => {
    const updatedCharges = chargeData.map((item) => {
      if (item.chargeKey === chargeKey) {
        return { ...item, [field]: value };
      }
      return item;
    });

    localDispatch({ type: ACTIONS.UPDATE_FIELDS, payload: { chargeData: updatedCharges } });
    updateLandedCost(data, updatedCharges, charge1Value);
  };

  return chargeData?.map((line) => (
    <div key={line?.chargeKey} className="form-calculator__field">
      <div className="form-calculator__field-name">
        {!user?.tenant_info?.purchase_config?.sub_modules?.purchase_order?.settings?.use_custom_charges ? (
          <div className="select_extra_charge_wrapper">
            <SelectExtraCharge
              containerClassName="orgInputContainer"
              selectedChargeName={line.charge_name}
              disabled={createGRNLoading || updateGRNLoading}
              onChange={(value) => handleChargeChange(value?.ledger_name, line.chargeKey, 'charge_name')}
              customStyle={{ width: '220px', backgroundColor: 'white' }}
              excludeCharges={chargeData?.map((item) => item?.charge_name)}
              entityName="PURCHASE"
            />
            {line?.chargesTax && (selectedSeller ? selectedSeller?.seller_info?.seller_type !== 'OVERSEAS' : selectedPoForGrn?.seller_info?.seller_type !== 'OVERSEAS') && (
              <div style={{ color: '#2d7df7', fontWeight: '400', fontSize: '12px' }}>
                {`tax@${line?.chargesTax}%`}
              </div>
            )}
          </div>
        ) : (
          <H3FormInput
            value={line?.charge_name}
            type="text"
            containerClassName={`orgInputContainer ${formSubmitted && Number(line?.charge_name) <= 0 ? 'form-error__input' : ''}`}
            placeholder="Charge name.."
            labelClassName="orgFormLabel"
            inputClassName="orgFormInput"
            onChange={(e) => handleChargeChange(e.target.value, line.chargeKey, 'charge_name')}
            disabled={createGRNLoading || updateGRNLoading}
          />
        )}
      </div>

      <div className="form-calculator__field-value" style={{ display: 'flex', gap: '0px' }}>
        <div style={{ width: '140px', marginRight: '-27px' }}>
          <H3FormInput
            value={line?.charge_amount}
            type="number"
            containerClassName={`orgInputContainer ${formSubmitted && Number(line?.charge_amount) <= 0 ? 'form-error__input' : ''}`}
            labelClassName="orgFormLabel"
            inputClassName="orgFormInput"
            onChange={(e) => handleChargeChange(Number.parseFloat(e.target.value) || 0, line.chargeKey, 'charge_amount')}
            allowNegative
            disabled={createGRNLoading || updateGRNLoading}
          />
        </div>

        <div className={isVendorOverseas ? 'display-none' : ''}>
          <ChargesTaxInput
            selectedTenant={selectedTenant}
            selectedChargeName={line.charge_name}
            openChargesTax={line?.openChargesTax}
            setOpenChargesTax={(value) => handleChargeChange(value, line.chargeKey, 'openChargesTax')}
            sacCode={line?.chargesSacCode}
            setSacCode={(value) => handleChargeChange(value, line.chargeKey, 'chargesSacCode')}
            chargesTaxId={line?.chargesTaxId}
            setChargesTaxData={(value) => {
              const updatedChargeData = chargeData.map((item) => {
                if (item.chargeKey === line.chargeKey) {
                  return {
                    ...item,
                    chargesTaxId: value?.tax_id ?? 'Not Applicable',
                    chargesTax: value?.tax_value ?? null,
                    chargesTaxInfo: value ?? null,
                    chargesTaxData: !value ? { child_taxes: [{ tax_amount: 0, tax_type_name: '' }] }
                      : { ...value, child_taxes: Helpers.computeTaxation(line?.charge_amount, value, getBillFromAddress(), getBillToAddress())?.tax_info?.child_taxes },
                  };
                }
                return item;
              });
              localDispatch({ type: ACTIONS.UPDATE_FIELDS, payload: {chargeData: updatedChargeData} });
            }}
            tallyLedgerName={line?.tallyLedgerName}
            setTallyLedgerName={(value) => handleChargeChange(value, line.chargeKey, 'tallyLedgerName')}
            showTallyLedgerPurchase
          />
        </div>

        <div className="form-calculator__delete-line-button" onClick={() => handleDeleteCharge(line?.chargeKey)}>
          <FontAwesomeIcon icon={faCircleXmark} size="sm" style={{ color: '#6f7276' }} />
        </div>
      </div>
    </div>
  ));
};

export default memo(ChargesRenderer);
