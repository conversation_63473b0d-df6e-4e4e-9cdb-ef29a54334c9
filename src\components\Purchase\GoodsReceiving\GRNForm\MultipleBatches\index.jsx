/* eslint-disable no-unused-expressions */
/* eslint-disable no-nested-ternary */
import React, { useEffect, useRef, useState } from 'react';
import { Link, withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import {
  Table, Popconfirm, notification, DatePicker,
} from 'antd';
import './style.scss';
import { v4 as uuidv4 } from 'uuid';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faFolderPlus, faTrashCan,
} from '@fortawesome/free-solid-svg-icons';
import dayjs from 'dayjs';
import H3FormInput from '@Uilib/h3FormInput';
import Helpers from '@Apis/helpers';
import CustomFieldLine from '@Components/Common/CustomFieldLine';
import WarehouseLocationSelector from '../../../../Common/Selector/WarehouseLocationSelector';

export function validateBatchDetailsSimple(config, batches, isApInvoiceEnabled) {
  // Helper function to check if a field is missing when it's mandatory
  const isFieldValid = (isEnabled, isMandatory, value) => {
    // If the field is enabled and mandatory, check if the value is valid
    if (isEnabled && isMandatory) {
      return !(value === undefined || value === '' || value === 0);
    }
    // If the field is not enabled or not mandatory, it's considered valid
    return true;
  };

  // eslint-disable-next-line no-restricted-syntax
  for (const batch of batches) {
    if (!['NON_STORABLE', 'SERVICE'].includes(batch?.product_sku_info?.product_type)) {
      // Check if multiple_batch_info is present and is an array

      if (Array.isArray(batch?.multiple_batch_info)) {
        // Iterate over each item in the multiple_batch_info array
        // eslint-disable-next-line no-restricted-syntax
        for (const item of batch.multiple_batch_info) {
          // Validate each mandatory field for each batch item
          if (
            isApInvoiceEnabled ? false : !isFieldValid(config?.cost_price_is_enabled, config?.cost_price_is_mandatory, item?.cost_price)
            || !isFieldValid(config?.custom_batch_number_is_enabled, config?.custom_batch_number_is_mandatory, item?.custom_batch_number)
            || !isFieldValid(config?.lot_number_is_enabled, config?.lot_number_is_mandatory, item?.lot_number)
            || isApInvoiceEnabled ? false : !isFieldValid(config?.selling_price_is_enabled, config?.selling_price_is_mandatory, item?.selling_price)
            || !isFieldValid(config?.mrp_is_enabled, config?.mrp_is_mandatory, item?.mrp)
            || !isFieldValid(config?.batch_barcode_is_enabled, config?.batch_barcode_is_mandatory, item?.batch_barcode)
            || !isFieldValid(config?.ar_number_is_enabled, config?.ar_number_is_mandatory, item?.ar_number)
            || !isFieldValid(config?.roll_no_is_enabled, config?.roll_no_is_mandatory, item?.roll_no)
            || !isFieldValid(config?.brand_is_enabled, config?.brand_is_mandatory, item?.brand)
            || !isFieldValid(config?.mfg_batch_no_is_enabled, config?.mfg_batch_no_is_mandatory, item?.mfg_batch_no)
            || !isFieldValid(config?.manufacturing_date_is_enabled, config?.manufacturing_date_is_mandatory, item?.manufacturing_date)
            // Include other conditions as needed
          ) {
            // If any field validation fails for an item, return false
            return false;
          }
        }
      }
    }
    // Check if multiple_batch_info is present and is an array
  }

  // If all batches pass validation, return true
  return true;
}

const MultipleBatchesSelector = ({
  innerBatchSelector, grnTableData, formSubmitted, updateGrnTableData, multipleBatchQuantity, customInputChangeProps,
  setMultipleBatchQuantity, grnTypeValue, destDepartmentId, enabledWarehouseLocations, multipleBatchData, org,
  isBatchValidValue, setIsBatchValid, billFromState, billToState, user, priceMasking, customField, updateBatchCfsForMultiBatchMode, isApInvoiceEnabled,
}) => {
  const [call, setCall] = useState(false);
  const batchConfig = org?.organisation?.[0]?.inventory_config?.settings?.batch_management;

  const previousGrnData = useRef(grnTableData);

  useEffect(() => {
    if (JSON.stringify(previousGrnData.current) !== JSON.stringify(grnTableData) && formSubmitted) {
      previousGrnData.current = grnTableData;
      const isValid = validateBatchDetailsSimple(batchConfig, grnTableData, isApInvoiceEnabled);
      if(isValid != isBatchValidValue) {
        setIsBatchValid(isValid);
      }
    }
  }, [grnTableData, batchConfig, formSubmitted, call]);

  const addNewBatchRow = () => {
    const tempData = [...multipleBatchData];
    // Find the max batch number in the existing data
    const currSkuCode = tempData?.[0]?.custom_batch_number?.split('/')[0];

    const maxBatchNumber = grnTableData?.reduce((max, data) => {
      if (data?.product_sku_info?.internal_sku_code == currSkuCode) {
        const value = data?.multiple_batch_info?.reduce((value, row) => {
          const currentNumber = parseInt(row?.custom_batch_number?.split('/')?.[2], 10);
          return currentNumber > value ? currentNumber : value;
        }, max);
        return value > max ? value : max;
      }
      return max;
    }, 0);

    // Increment the max batch number to get the new batch number
    const newBatchNumber = maxBatchNumber + 1;
    const newBatchObj = {
      ...multipleBatchData?.[0],
      key: uuidv4(),
      custom_batch_number: `${multipleBatchData?.[0]?.custom_batch_number?.split('/')?.[0]}/${multipleBatchData?.[0]?.custom_batch_number?.split('/')?.[1]}/${newBatchNumber}`,
      batch_qyt: 0,
      // cost_price: 0,  pre fill the cost price
      cost_price: multipleBatchData?.[0]?.cost_price,
      mrp: 0,
      expiry_date: multipleBatchData?.[0]?.expiry_date,
      // inventory_location_id: null,
      // inventory_location_path: null,
      inventory_location_id: multipleBatchData?.[0]?.inventory_location_id,
      inventory_location_path: multipleBatchData?.[0]?.inventory_location_path,
      selling_price: 0,
      lot_number: null,
      parentKey: multipleBatchData?.[0]?.parentKey,
      quantity: '',
      ar_number: null,
      custom_fields: customField?.map(field => ({
        ...field,
        fieldValue: null,
      })),
    };

    if (tempData?.length >= 5) {
      notification.open({
        type: 'error',
        message: 'You can add only 5 batches at a time',
        duration: 4,
        placement: 'top',
      });
    } else {
      tempData.push(newBatchObj);
      const copyData = JSON.parse(JSON.stringify(grnTableData));
      const updatedData = copyData?.map((obj) => {
        if (obj?.key === multipleBatchData?.[0]?.parentKey) {
          return {
            ...obj,
            multiple_batch_info: tempData,
          };
        }
        return obj;
      });
      updateGrnTableData(updatedData);
    }
  };
  const deleteBatchRow = (key) => {
    const tempData = [...multipleBatchData];
    const updatedData = tempData?.filter((item) => item.key !== key);

    // Check if the length is less than one
    if (updatedData.length < 1) {
      notification.open({
        type: 'error',
        message: 'Minimum one batch is required',
        duration: 4,
        placement: 'top',
      });
      return;
    }

    const copyData = JSON.parse(JSON.stringify(grnTableData));
    const updatedData1 = copyData.map((obj) => {
      if (obj?.key === updatedData?.[0]?.parentKey) {
        return {
          ...obj,
          multiple_batch_info: updatedData,
        };
      }
      return obj;
    });
    updateGrnTableData(updatedData1);
  };

  useEffect(() => {
    const calculateTotalMultipleBatchQuantity = () => {
      let total = 0;
      multipleBatchData?.forEach((item) => {
        total += (Number(item?.quantity));
      });

      return total;
    };

    if (grnTypeValue === 'ADHOC') {
      grnTableData?.forEach((obj) => {
        if ((obj?.key === multipleBatchData?.[0]?.parentKey && obj?.key) || (obj?.grn_line_id === multipleBatchData?.[0]?.grn_line_id && obj?.grn_line_id)) {
          const discountValue = Number(obj?.discount) || 0;
          const totalPrice = (Number(calculateTotalMultipleBatchQuantity()) * obj.offer_price);
          const taxableValue = obj?.lineDiscountType === 'Percent' ? totalPrice * (1 - discountValue / 100)
            : Math.max(totalPrice - discountValue, 0);
          obj.quantity = calculateTotalMultipleBatchQuantity();
          obj.child_taxes = Helpers.computeTaxation(taxableValue, obj?.taxInfo, billFromState, billToState)?.tax_info?.child_taxes;
          setMultipleBatchQuantity(calculateTotalMultipleBatchQuantity());
        }
      });
    } else {
      grnTableData?.forEach((obj) => {
        if ((obj?.key === multipleBatchData?.[0]?.parentKey && obj?.key) || (obj?.grn_line_id === multipleBatchData?.[0]?.grn_line_id && obj?.grn_line_id)) {
          const discountValue = Number(obj?.discount) || 0;
          const totalPrice = (Number(calculateTotalMultipleBatchQuantity()) * obj.offer_price);
          const taxableValue = obj?.lineDiscountType === 'Percent' ? totalPrice * (1 - discountValue / 100)
            : Math.max(totalPrice - discountValue, 0);
          if (obj?.po_line_status?.length === 0) obj.received_qty = calculateTotalMultipleBatchQuantity();
          obj.child_taxes = Helpers.computeTaxation(taxableValue, obj?.taxInfo, billFromState, billToState)?.tax_info?.child_taxes;
          setMultipleBatchQuantity(calculateTotalMultipleBatchQuantity());
        }
      });
    }
  }, [multipleBatchData, grnTableData, grnTypeValue, call]);

  const { isDataMaskingPolicyEnable, isHideCostPrice, isHideSellingPrice } = priceMasking;

  const getColumns = () => {
    const batchColumnsInner = [
      {
        title: 'Batch #',
        width: '150px',
        render: (item) => (
          <div>
            <H3FormInput
              inputClassName={`orgFormInput ${(!item.custom_batch_number) ? 'orgFormInputError' : ''} `}
              value={(item?.custom_batch_number || item?.batch_number) ? (item?.custom_batch_number || item?.batch_number) : ''}
              name="batch number"
              type="text"
              disabled={item?.available_batches?.[0]?.batch_id}
              onChange={(event) => {
                const copyData = JSON.parse(JSON.stringify(grnTableData));
                const updatedData = copyData.map((obj) => {
                  if ((obj.po_line_id === item.po_line_id && obj?.po_line_id) || (obj?.grn_line_id === item?.grn_line_id && item?.grn_line_id) || (obj?.key === item?.parentKey && obj?.key)) {
                    const updatedMultipleBatchInfo = obj?.multiple_batch_info?.map((item1) => {
                      if (item1?.key === item?.key) {
                        return {
                          ...item1,
                          custom_batch_number: event?.target?.value,
                        };
                      }
                      return item1;
                    });
                    return {
                      ...obj,
                      multiple_batch_info: updatedMultipleBatchInfo,
                    };
                  }
                  return obj;
                });
                updateGrnTableData(updatedData);
              }}
            />
          </div>
        ),
      },
      {
        title: 'Quantity',
        width: '100px',
        render: (item) => (
          <H3FormInput
            value={item?.quantity}
            type="number"
            containerClassName={`orgInputContainer ${(Number(item.quantity) <= 0 && formSubmitted) ? 'create-pr__input' : ''}`}
            labelClassName="orgFormLabel"
            inputClassName="orgFormInput"
            onChange={(event) => {
              const copyData = JSON.parse(JSON.stringify(grnTableData));
              const updatedData = copyData.map((obj) => {
                if ((obj.po_line_id === item.po_line_id && obj?.po_line_id) || (obj?.grn_line_id === item?.grn_line_id && item?.grn_line_id) || (obj?.key === item?.parentKey && obj?.key)) {
                  const updatedMultipleBatchInfo = obj?.multiple_batch_info?.map((item1) => {
                    setCall(!call);
                    if (item1?.key === item?.key) {
                      return {
                        ...item1,
                        quantity: event?.target?.value,
                      };
                    }
                    return item1;
                  });
                  const cfId = obj?.lineCustomFields?.find((cf) => cf.fieldName === 'Quantity')?.cfId;
                  const totalQty = updatedMultipleBatchInfo?.reduce((acc, item) => acc + (Number(item.quantity) || 0), 0);
                  return {
                    ...obj,
                    multiple_batch_info: updatedMultipleBatchInfo,
                    lineCustomFields: customInputChangeProps(totalQty, cfId, obj, true),

                  };
                }
                return obj;
              });
              updateGrnTableData(updatedData);
              setCall(!call);
            }}
            showError={(formSubmitted && (Number(item.quantity) <= 0))}
            name="quantity"
            onBlur={() => {
              setCall(!call);
              updateGrnTableData(grnTableData);
            }}
          />
        ),
      },
      {
        title: 'Lot #',
        render: (item) => (
          <H3FormInput
            labelClassName="orgFormLabel"
            // inputClassName={`orgFormInput ${ (formSubmitted && !item?.lot_number) ? 'orgFormInputError' : ''} `}
            inputClassName={`orgFormInput ${(formSubmitted && batchConfig?.lot_number_is_mandatory && !item?.lot_number) ? 'orgFormInputError' : ''} `}
            value={item?.lot_number ? item?.lot_number : ''}
            name="lot number"
            type="text"
            onChange={(event) => {
              const copyData = JSON.parse(JSON.stringify(grnTableData));
              const updatedData = copyData.map((obj) => {
                if ((obj.po_line_id === item.po_line_id && obj?.po_line_id) || (obj?.grn_line_id === item?.grn_line_id && item?.grn_line_id) || (obj?.key === item?.parentKey && obj?.key)) {
                  const updatedMultipleBatchInfo = obj?.multiple_batch_info?.map((item1) => {
                    if (item1?.key === item?.key) {
                      return {
                        ...item1,
                        lot_number: event?.target?.value,
                      };
                    }
                    return item1;
                  });
                  return {
                    ...obj,
                    multiple_batch_info: updatedMultipleBatchInfo,
                  };
                }
                return obj;
              });
              updateGrnTableData(updatedData);
              setCall(!call);
            }}
          />
        ),
        hidden: !batchConfig?.lot_number_is_enabled,
      },
      {
        title: 'CP',
        width: '70px',
        render: (item) => (
          <H3FormInput
            value={item?.cost_price ? item?.cost_price : ''}
            type="number"
            // containerClassName={(formSubmitted && !item?.cost_price) ? 'create-pr__input' : ''}
            labelClassName="orgFormLabel"
            inputClassName={`orgFormInput ${(formSubmitted && batchConfig?.cost_price_is_mandatory && !item?.cost_price) ? 'orgFormInputError' : ''} `}
            onChange={(event) => {
              const copyData = JSON.parse(JSON.stringify(grnTableData));
              const updatedData = copyData.map((obj) => {
                if ((obj.po_line_id === item.po_line_id && obj?.po_line_id) || (obj?.grn_line_id === item?.grn_line_id && item?.grn_line_id) || (obj?.key === item?.parentKey && obj?.key)) {
                  const updatedMultipleBatchInfo = obj?.multiple_batch_info?.map((item1) => {
                    if (item1?.key === item?.key) {
                      return {
                        ...item1,
                        cost_price: event?.target?.value,
                      };
                    }
                    return item1;
                  });
                  return {
                    ...obj,
                    multiple_batch_info: updatedMultipleBatchInfo,
                  };
                }
                return obj;
              });
              updateGrnTableData(updatedData);
              setCall(!call);
            }}
            hideInput={isDataMaskingPolicyEnable && isHideCostPrice}
            popOverMessage="You don't have access to view cost price"
          />
        ),
        hidden: !batchConfig?.cost_price_is_enabled || isApInvoiceEnabled,
      },
      {
        title: 'SP',
        width: '70px',
        render: (item) => (
          <div>
            <H3FormInput
              value={item?.selling_price ? item?.selling_price : ''}
              type="number"
              // containerClassName={` ${(formSubmitted && !item?.selling_price) ? 'create-pr__input' : ''}`}
              labelClassName="orgFormLabel"
              inputClassName={`orgFormInput ${(formSubmitted && batchConfig?.selling_price_is_mandatory && !item?.selling_price) ? 'orgFormInputError' : ''} `}
              onChange={(event) => {
                const copyData = JSON.parse(JSON.stringify(grnTableData));
                const updatedData = copyData.map((obj) => {
                  if ((obj.po_line_id === item.po_line_id && obj?.po_line_id) || (obj?.grn_line_id === item?.grn_line_id && item?.grn_line_id) || (obj?.key === item?.parentKey && obj?.key)) {
                    const updatedMultipleBatchInfo = obj?.multiple_batch_info?.map((item1) => {
                      if (item1?.key === item?.key) {
                        return {
                          ...item1,
                          selling_price: event?.target?.value,
                        };
                      }
                      return item1;
                    });
                    return {
                      ...obj,
                      multiple_batch_info: updatedMultipleBatchInfo,
                    };
                  }
                  return obj;
                });
                updateGrnTableData(updatedData);
                setCall(!call);
              }}
              hideInput={isDataMaskingPolicyEnable && isHideSellingPrice}
              popOverMessage="You don't have access to view selling price"
            />
          </div>
        ),
        hidden: !batchConfig?.selling_price_is_enabled,
      },
      {
        title: 'MRP',
        width: '70px',
        render: (item) => (
          <H3FormInput
            value={item?.mrp ? item?.mrp : ''}
            type="number"
            // containerClassName={` ${(formSubmitted && !item?.mrp) ? 'create-pr__input' : ''}`}
            labelClassName="orgFormLabel"
            inputClassName={`orgFormInput ${(formSubmitted && batchConfig?.mrp_is_mandatory && !item?.mrp) ? 'orgFormInputError' : ''} `}
            onChange={(event) => {
              const copyData = JSON.parse(JSON.stringify(grnTableData));
              const updatedData = copyData.map((obj) => {
                if ((obj.po_line_id === item.po_line_id && obj?.po_line_id) || (obj?.grn_line_id === item?.grn_line_id && item?.grn_line_id) || (obj?.key === item?.parentKey && obj?.key)) {
                  const updatedMultipleBatchInfo = obj?.multiple_batch_info?.map((item1) => {
                    if (item1?.key === item?.key) {
                      return {
                        ...item1,
                        mrp: event?.target?.value,
                      };
                    }
                    return item1;
                  });
                  return {
                    ...obj,
                    multiple_batch_info: updatedMultipleBatchInfo,
                  };
                }
                return obj;
              });
              updateGrnTableData(updatedData);
              setCall(!call);
            }}
          />
        ),
        hidden: !batchConfig?.mrp_is_enabled,
      },
      {
        title: 'AR Number',
        render: (item) => (
          <H3FormInput
            value={item?.ar_number ? item?.ar_number : ''}
            type="text"
            // containerClassName={(formSubmitted && !item?.ar_number) ? 'create-pr__input' : ''}
            labelClassName="orgFormLabel"
            inputClassName={`orgFormInput ${(formSubmitted && batchConfig?.ar_number_is_mandatory && !item?.ar_number) ? 'orgFormInputError' : ''} `}
            onChange={(event) => {
              const copyData = JSON.parse(JSON.stringify(grnTableData));
              const updatedData = copyData.map((obj) => {
                if ((obj.po_line_id === item.po_line_id && obj?.po_line_id) || (obj?.grn_line_id === item?.grn_line_id && item?.grn_line_id) || (obj?.key === item?.parentKey && obj?.key)) {
                  const updatedMultipleBatchInfo = obj?.multiple_batch_info?.map((item1) => {
                    if (item1?.key === item?.key) {
                      return {
                        ...item1,
                        ar_number: event?.target?.value,
                      };
                    }
                    return item1;
                  });
                  return {
                    ...obj,
                    multiple_batch_info: updatedMultipleBatchInfo,
                  };
                }
                return obj;
              });
              updateGrnTableData(updatedData);
              setCall(!call);
            }}
          />
        ),
        hidden: !batchConfig?.ar_number_is_enabled,
      },
      {
        title: 'Expiry',
        render: (item) => (
          <DatePicker
            value={dayjs(item?.expiry_date)}
            onChange={(event) => {
              const copyData = JSON.parse(JSON.stringify(grnTableData));
              const updatedData = copyData.map((obj) => {
                if ((obj.po_line_id === item.po_line_id && obj?.po_line_id) || (obj?.grn_line_id === item?.grn_line_id && item?.grn_line_id) || (obj?.key === item?.parentKey && obj?.key)) {
                  const updatedMultipleBatchInfo = obj?.multiple_batch_info?.map((item1) => {
                    if (item1?.key === item?.key) {
                      return {
                        ...item1,
                        expiry_date: event,
                      };
                    }
                    return item1;
                  });
                  return {
                    ...obj,
                    multiple_batch_info: updatedMultipleBatchInfo,
                  };
                }
                return obj;
              });
              updateGrnTableData(updatedData);
            }}
            format={item?.expiryDateFormat}
            picker={item?.expiryDateFormat !== 'DD/MM/YYYY' ? 'month' : ''}
            className="multiple-batches__input-date"
          />
        ),
        hidden: multipleBatchData?.[0]?.expiryDays < 0,
      },
      {
        title: 'Shelf/Rack',
        width: 150,
        render: (item) => (
          <WarehouseLocationSelector
            containerClassName="multiple-batches__input-warehouse-location"
            hideTitle
            selectedInventoryLocation={item?.inventory_location_path}
            onChange={(id, name) => {
              const copyData = JSON.parse(JSON.stringify(grnTableData));
              const updatedData = copyData.map((obj) => {
                if ((obj.po_line_id === item.po_line_id && obj?.po_line_id) || (obj?.grn_line_id === item?.grn_line_id && item?.grn_line_id) || (obj?.key === item?.parentKey && obj?.key)) {
                  const updatedMultipleBatchInfo = obj?.multiple_batch_info?.map((item1) => {
                    if (item1?.key === item?.key) {
                      return {
                        ...item1,
                        inventory_location_id: id,
                        inventory_location_path: name,
                      };
                    }
                    return item1;
                  });
                  return {
                    ...obj,
                    multiple_batch_info: updatedMultipleBatchInfo,
                  };
                }
                return obj;
              });
              updateGrnTableData(updatedData);
              setCall(!call);
            }}
            destDepartmentId={destDepartmentId}
          />
        ),
        hidden: !enabledWarehouseLocations,
      },
      {
        title: 'Barcode',
        render: (item) => (
          <H3FormInput
            value={item?.batch_barcode ? item?.batch_barcode : ''}
            type="text"
            // containerClassName={(formSubmitted && !item?.ar_number) ? 'create-pr__input' : ''}
            labelClassName="orgFormLabel"
            inputClassName={`orgFormInput ${(formSubmitted && batchConfig?.batch_barcode_is_mandatory && !item?.batch_barcode) ? 'orgFormInputError' : ''} `}
            onChange={(event) => {
              const copyData = JSON.parse(JSON.stringify(grnTableData));
              const updatedData = copyData.map((obj) => {
                if ((obj.po_line_id === item.po_line_id && obj?.po_line_id) || (obj?.grn_line_id === item?.grn_line_id && item?.grn_line_id) || (obj?.key === item?.parentKey && obj?.key)) {
                  const updatedMultipleBatchInfo = obj?.multiple_batch_info?.map((item1) => {
                    if (item1?.key === item?.key) {
                      return {
                        ...item1,
                        batch_barcode: event?.target?.value,
                      };
                    }
                    return item1;
                  });
                  return {
                    ...obj,
                    multiple_batch_info: updatedMultipleBatchInfo,
                  };
                }
                return obj;
              });
              updateGrnTableData(updatedData);
              setCall(!call);
            }}
          />
        ),
        hidden: !batchConfig?.batch_barcode_is_enabled,
      },
      {
        title: 'Roll No',
        render: (item) => (
          <H3FormInput
            value={item?.roll_no ? item?.roll_no : ''}
            type="text"
            labelClassName="orgFormLabel"
            inputClassName={`orgFormInput ${(formSubmitted && batchConfig?.roll_no_is_mandatory && !item?.roll_no) ? 'orgFormInputError' : ''} `}
            onChange={(event) => {
              const copyData = JSON.parse(JSON.stringify(grnTableData));
              const updatedData = copyData.map((obj) => {
                if ((obj.po_line_id === item.po_line_id && obj?.po_line_id) || (obj?.grn_line_id === item?.grn_line_id && item?.grn_line_id) || (obj?.key === item?.parentKey && obj?.key)) {
                  const updatedMultipleBatchInfo = obj?.multiple_batch_info?.map((item1) => {
                    if (item1?.key === item?.key) {
                      return {
                        ...item1,
                        roll_no: event?.target?.value,
                      };
                    }
                    return item1;
                  });
                  return {
                    ...obj,
                    multiple_batch_info: updatedMultipleBatchInfo,
                  };
                }
                return obj;
              });
              updateGrnTableData(updatedData);
              setCall(!call);
            }}
          />
        ),
        hidden: !batchConfig?.roll_no_is_enabled,
      },
      {
        title: 'Mfg Batch No',
        render: (item) => (
          <H3FormInput
            value={item?.mfg_batch_no ? item?.mfg_batch_no : ''}
            type="text"
            // containerClassName={(formSubmitted && !item?.ar_number) ? 'create-pr__input' : ''}
            labelClassName="orgFormLabel"
            inputClassName={`orgFormInput ${(formSubmitted && batchConfig?.mfg_batch_no_is_mandatory && !item?.mfg_batch_no) ? 'orgFormInputError' : ''} `}
            onChange={(event) => {
              const copyData = JSON.parse(JSON.stringify(grnTableData));
              const updatedData = copyData.map((obj) => {
                if ((obj.po_line_id === item.po_line_id && obj?.po_line_id) || (obj?.grn_line_id === item?.grn_line_id && item?.grn_line_id) || (obj?.key === item?.parentKey && obj?.key)) {
                  const updatedMultipleBatchInfo = obj?.multiple_batch_info?.map((item1) => {
                    if (item1?.key === item?.key) {
                      return {
                        ...item1,
                        mfg_batch_no: event?.target?.value,
                      };
                    }
                    return item1;
                  });
                  return {
                    ...obj,
                    multiple_batch_info: updatedMultipleBatchInfo,
                  };
                }
                return obj;
              });
              updateGrnTableData(updatedData);
              setCall(!call);
            }}
          />
        ),
        hidden: !batchConfig?.mfg_batch_no_is_enabled,
      },
      {
        title: 'Mfg Date',
        render: (item) => (
          <DatePicker
            value={item?.manufacturing_date ? dayjs(item?.manufacturing_date) : ''}
            onChange={(event) => {
              const copyData = JSON.parse(JSON.stringify(grnTableData));
              const updatedData = copyData.map((obj) => {
                if ((obj.po_line_id === item.po_line_id && obj?.po_line_id) || (obj?.grn_line_id === item?.grn_line_id && item?.grn_line_id) || (obj?.key === item?.parentKey && obj?.key)) {
                  const updatedMultipleBatchInfo = obj?.multiple_batch_info?.map((item1) => {
                    if (item1?.key === item?.key) {
                      return {
                        ...item1,
                        manufacturing_date: event,
                      };
                    }
                    return item1;
                  });
                  return {
                    ...obj,
                    multiple_batch_info: updatedMultipleBatchInfo,
                  };
                }
                return obj;
              });
              updateGrnTableData(updatedData);
              setCall(!call);
            }}
            format={item?.manufacturingDateFormat}
            picker={item?.manufacturingDateFormat !== 'DD/MM/YYYY' ? 'month' : ''}
            className={`multiple-batches__input-date ${(formSubmitted && batchConfig?.manufacturing_date_is_mandatory && !item?.manufacturing_date) ? 'orgFormInputError' : ''}`}
          />
        ),
        hidden: !batchConfig?.manufacturing_date_is_enabled,
      },
      {
        title: '',
        fixed: 'right',
        render: (text, record) => (
          <Popconfirm
            title="Are you sure you want to remove this Batch?"
            onConfirm={() => {
              setCall((prevCall) => !prevCall);
              deleteBatchRow(record?.key, record?.grn_line_id);
            }}
            placement="left"
          >
            <div className="delete-line-button__new" style={{ marginLeft: 'auto' }}>
              <FontAwesomeIcon icon={faTrashCan} />
            </div>
          </Popconfirm>
        ),
      },
    ];
    const columns = customField?.map((cfs) => ({
      title: cfs.fieldName, // Dynamic column title
      key: cfs.cfId,
      render: (item) => {
        return (
          <CustomFieldLine
            customFields={item?.custom_fields || []}
            cfId={cfs.cfId}
            labelClassName="orgFormLabel"
            inputClassName="orgFormInput"
            formSubmitted={formSubmitted}
            customLineInputChange={(value) => {
              // Find the object that matches the cfs.cfId
              const matchedObj = value?.find(obj => obj.cfId === cfs.cfId);
              updateBatchCfsForMultiBatchMode(matchedObj?.fieldValue, cfs.cfId, item);
            }}
          />
        );
      },
    }));
    const insertIndex = batchColumnsInner.length - 1;
    batchColumnsInner.splice(insertIndex, 0, ...columns);
    return batchColumnsInner.filter((item) => !item.hidden);
  };
  return (
    <div className={`batch-selector-wrapper ${innerBatchSelector ? 'inner-batch-selector-wrapper' : ''}`}>
      <Table
        size="small"
        // columns={batchColumnsInner}
        columns={getColumns()}
        dataSource={multipleBatchData}
        pagination={false}
        bordered
        scroll={{ x: 'max-content' }}
      />
      <div
        className="new-Batch-button"
        onClick={() => addNewBatchRow()}
      >
        <span className="new-Batch-button__icon">
          <FontAwesomeIcon icon={faFolderPlus} />
        </span>
        <div>New Batch</div>
      </div>
    </div>
  );
};

const mapStateToProps = ({
  UserReducers,
}) => ({
  user: UserReducers.user,
  MONEY: UserReducers.MONEY,
  org: UserReducers.org,
  priceMasking: UserReducers.priceMasking,
});

MultipleBatchesSelector.propTypes = {
  user: PropTypes.any,
  innerBatchSelector: PropTypes.bool,
  MONEY: PropTypes.func,
  grnTableData: PropTypes.any,
  formSubmitted: PropTypes.bool,
  updateGrnTableData: PropTypes.func,
  multipleBatchQuantity: PropTypes.any,
  setMultipleBatchQuantity: PropTypes.func,
  grnTypeValue: PropTypes.any,
  destDepartmentId: PropTypes.any,
  enabledWarehouseLocations: PropTypes.any,
  multipleBatchData: PropTypes.any,
  org: PropTypes.any,
};

export default connect(mapStateToProps)(withRouter(MultipleBatchesSelector));
