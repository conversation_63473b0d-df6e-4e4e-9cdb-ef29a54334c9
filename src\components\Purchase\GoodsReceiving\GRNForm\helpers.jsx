/* eslint-disable unicorn/prefer-structured-clone */
import { notification, Select } from 'antd';
import React, { useEffect, useState } from 'react';
import Constants, { DEFAULT_CUR_ROUND_OFF, INFINITE_EXPIRY_DATE, toISTDate } from '@Apis/constants';
import PRZSelect from '../../../Common/UI/PRZSelect';
import HideValue from '@Components/Common/RestrictedAccess/HideValue';
import Helpers from '@Apis/helpers';
import CustomFieldHelpers from '@Helpers/CustomFieldHelpers';
import FormHelpers from '@Helpers/FormHelpers';
import { v4 as uuidv4 } from 'uuid';
import dayjs from 'dayjs';

const { Option } = Select;

export const generateGRNPayload = (params) => {
  const {
    // Form state
    data,
    grnTypeValue,
    approve,
    isMultipleBatchModeEnabled,
    tenantDepartmentId,

    // GRN details
    grnDate,
    invoiceDate,
    invoiceNumber,
    terms,
    dueDate,
    narration,

    // Seller/Vendor info
    selectedSeller,
    selectedPoForGrn,
    selectedPo,
    selectedGRN,
    vendorAddress,
    gstNumber,

    // Financial details
    chargeData,
    discountPercentage,
    discountType,
    isGRNLevelDiscount,
    charge1Name,
    charge1Value,
    taxTypeInfo,
    taxTypeName,
    taxType,

    // Currency and conversion
    selectedCurrencyInfo,
    currencyConversionRate,

    // Transport details
    transporterId,
    vehicleNumber,
    freightTaxId,
    freightTaxInfo,
    freightSacCode,
    transporterBillNumber,

    // Attachments and notifications
    fileList,
    ewayBillList,
    ewayBillNumber,
    toRecipients,
    checkedRecipients,

    // Custom fields
    cfGoodReceivingNotesDoc,

    // Other details
    selectedTenant,
    purchaseAccount,
    updateDocumentReason,
    grnNumber,
    initialGrnNumber,
    docSeqId,
    paymentTerms,
    paymentRemarks,

    // Flags
    isApInvoiceEnabled,

    // Helper function
    getLineTotals,

    grnId,
  } = params;

  // Determine the scenario
  const isUpdate = !!grnId;
  const isFromPoScreen = !isUpdate && selectedPoForGrn?.po_id;
  const isFromPoSelection = !isUpdate && !selectedPoForGrn?.po_id && grnTypeValue !== 'ADHOC';
  const isAdhoc = !isUpdate && grnTypeValue === 'ADHOC';

  // Generate GRN lines based on scenario
  const grnLines = generateGRNLines({
    data,
    isUpdate,
    isFromPoScreen,
    isFromPoSelection,
    isAdhoc,
    isMultipleBatchModeEnabled,
    tenantDepartmentId,
    selectedGRN,
  });

  // Generate base payload structure
  const basePayload = {
    grn_lines: grnLines,
    notification_recipients: toRecipients,
    is_automatic_notification_enabled: checkedRecipients,
    grn_date_time: grnDate,
    invoice_number: invoiceNumber || null,
    invoice_date: invoiceDate || null,
    status: approve ? 'ISSUED' : 'DRAFT',
    remark: terms || '',
    grn_due_date: dueDate || null,
    attachments: fileList?.map((attachment) => ({
      url: attachment?.response?.response?.location || attachment?.url,
      type: attachment.type,
      name: attachment.name,
      uid: attachment.uid,
    })) || [],
    custom_fields: CustomFieldHelpers.postCfStructure(cfGoodReceivingNotesDoc),
    other_charges: chargeData?.map((charge) => ({
      charge_name: charge?.charge_name,
      charge_amount: charge?.charge_amount,
      charge_type: '',
      charge_sac_code: charge?.chargesSacCode || null,
      tax_info: charge?.chargesTaxInfo || null,
      ledger_name: charge?.tallyLedgerName || null,
    })) || [],
    discount_percentage: discountType === 'Percent'
      ? discountPercentage
      : (discountPercentage / (getLineTotals().totalAmount || 0)) * 100,
    is_discount_in_percent: discountType === 'Percent',
    is_line_wise_discount: !isGRNLevelDiscount,
    tcs_id: (taxTypeInfo && taxTypeName === 'TCS') ? taxType : null,
    tcs_info: (taxTypeInfo && taxTypeName === 'TCS') ? {
      tax_id: taxTypeInfo?.tax_id,
      tax_name: taxTypeInfo?.tax_name,
      tax_value: taxTypeInfo?.tax_value,
      tax_type_name: taxTypeInfo?.tax_type_name,
    } : null,
    tds_id: (taxTypeInfo && taxTypeName === 'TDS') ? taxType : null,
    tds_info: (taxTypeInfo && taxTypeName === 'TDS') ? {
      tax_id: taxTypeInfo?.tax_id,
      tax_name: taxTypeInfo?.tax_name,
      tax_value: taxTypeInfo?.tax_value,
      tax_type_name: taxTypeInfo?.tax_type_name,
    } : null,
    org_currency_id: selectedCurrencyInfo?.org_currency_id,
    charge_1_name: charge1Name,
    charge_1_value: isFromPoScreen && isApInvoiceEnabled ? 0 : Number(charge1Value),
    transporter_id: transporterId,
    vehicle_number: vehicleNumber,
    freight_tax_id: freightTaxId === 'Not Applicable' ? null : freightTaxId,
    freight_tax_info: freightTaxInfo,
    freight_sac_code: freightSacCode,
    transporter_bill_number: transporterBillNumber,
    e_way_bill: ewayBillList?.map((attachment) => ({
      url: attachment?.response?.response?.location || attachment?.url,
      type: attachment.type,
      name: attachment.name,
      uid: attachment.uid,
    })) || [],
    e_way_bill_number: ewayBillNumber,
    narration: narration || '',
    grn_number: initialGrnNumber?.toLowerCase()?.trim() === grnNumber?.toLowerCase()?.trim() ? null : grnNumber,
    allow_grn_to_create_ap_invoice: isApInvoiceEnabled,
    payment_terms: [
      {
        advance_amount: 0,
        due_days: paymentTerms || 0,
        remark: paymentRemarks || '',
      },
    ],
  };

  // Add scenario-specific fields
  if (isUpdate) {
    return {
      ...basePayload,
      tenant_seller_info: {
        ...selectedGRN?.tenant_seller_info,
        gst_number: gstNumber,
        seller_type: selectedSeller?.seller_type
      },
      tenant_department_id: selectedGRN?.tenant_department_id,
      grn_tenant_id: selectedTenant || selectedGRN?.grn_tenant_id,
      grn_id: selectedGRN?.grn_id,
      seller_id: selectedGRN?.seller_id || null,
      seller_address_info: vendorAddress,
      tally_purchase_account: purchaseAccount,
      grn_entity_type: selectedGRN?.grn_entity_type,
      conversion_rate: currencyConversionRate || selectedCurrencyInfo?.conversion_rate,
      update_document_reason: updateDocumentReason,
    };
  }

  if (isFromPoScreen) {
    return {
      ...basePayload,
      grn_entity_id: selectedPoForGrn?.po_id,
      seller_id: selectedPoForGrn?.tenant_seller_info?.seller_id,
      tenant_department_id: tenantDepartmentId,
      tenant_seller_info: {
        ...selectedPoForGrn?.tenant_seller_info,
        internal_slr_code: selectedPoForGrn?.seller_info?.internal_slr_code,
        gst_number: gstNumber
      },
      seller_address_info: selectedPoForGrn?.seller_address,
      tally_purchase_account: purchaseAccount,
      grn_tenant_id: selectedTenant || selectedPoForGrn?.tenant_id,
      grn_entity_type: 'PURCHASE_ORDER',
      conversion_rate: currencyConversionRate || selectedCurrencyInfo?.conversion_rate,
      is_job_works_grn: selectedPoForGrn?.is_job_works_po,
      production_route_id: selectedPoForGrn?.production_route_id,
      product_sku_id: selectedPoForGrn?.product_sku_id,
      seq_id: docSeqId || null,
    };
  }

  if (isFromPoSelection) {
    return {
      ...basePayload,
      tenant_seller_info: grnTypeValue === 'MULTIPO' ? {
        seller_id: selectedSeller?.seller_info?.seller_id,
        seller_name: selectedSeller?.seller_info?.seller_name,
        tenant_seller_id: selectedSeller?.tenant_seller_id,
        gst_number: gstNumber,
        seller_type: selectedSeller?.seller_info?.seller_type,
      } : {
        ...selectedPo?.tenant_seller_info,
        internal_slr_code: selectedGRN?.internal_slr_code,
        gst_number: gstNumber
      },
      seller_address_info: grnTypeValue === 'MULTIPO' ? vendorAddress : null,
      tenant_department_id: grnTypeValue === 'MULTIPO' ? tenantDepartmentId : selectedPo?.tenant_department_id,
      grn_entity_id: selectedPo?.po_id,
      seller_id: selectedSeller?.seller_info?.seller_id || null,
      grn_tenant_id: selectedTenant || selectedPo?.tenant_id,
      grn_entity_type: 'PURCHASE_ORDER',
      tally_purchase_account: purchaseAccount,
      conversion_rate: grnTypeValue === 'MULTIPO'
        ? selectedSeller?.currency_info?.conversion_rate
        : selectedPo?.conversion_rate,
      seq_id: docSeqId || null,
      seller_type: grnTypeValue === 'MULTIPO'
        ? selectedSeller?.seller_type
        : selectedPo?.tenant_seller_info?.seller_type,
    };
  }

  if (isAdhoc) {
    return {
      ...basePayload,
      tenant_seller_info: {
        tenant_seller_id: selectedSeller?.tenant_seller_id,
        gst_number: gstNumber,
        ...selectedSeller?.seller_info,
        seller_type: selectedSeller?.seller_info?.seller_type,
      },
      tenant_department_id: tenantDepartmentId,
      grn_entity_id: null,
      seller_id: selectedSeller?.seller_info?.seller_id || null,
      seller_address_info: vendorAddress,
      tally_purchase_account: purchaseAccount,
      grn_tenant_id: selectedTenant,
      grn_entity_type: 'GOOD_RECEIVING_NOTE',
      conversion_rate: currencyConversionRate || selectedCurrencyInfo?.conversion_rate,
      seq_id: docSeqId || null,
      seller_type: selectedSeller?.seller_type,
    };
  }

  throw new Error('Unable to determine GRN payload scenario');
};

const generateGRNLines = (params) => {
  const {
    data,
    isUpdate,
    isFromPoScreen,
    isFromPoSelection,
    isAdhoc,
    isMultipleBatchModeEnabled,
    tenantDepartmentId,
    selectedGRN,
  } = params;

  if (isUpdate) {
    return data?.map((item) => {
      const lineQuantity = selectedGRN?.grn_entity_type === 'GOOD_RECEIVING_NOTE'
        ? Number(item?.quantity)
        : Number(item?.received_qty);

      return {
        grn_entity_line_id: item.grn_entity_line_id || null,
        is_adhoc_line: !item.grn_entity_line_id,
        tenant_product_id: item.tenant_product_id || '',
        quantity: lineQuantity,
        invoice_quantity: item?.invoiceQuantity ?? lineQuantity,
        secondary_uom_qty: item?.secondaryUomId ? item?.secondary_uom_qty : 0,
        offer_price: Number(item.offer_price) || 0,
        tax_id: item?.taxId || '',
        tax_group_info: item?.taxInfo,
        uom_id: item?.uomId || '',
        uom_info: Array.isArray(item?.uom_info) ? item?.uom_info : (item?.uom_info ? [item?.uom_info] : []),
        expiry_date: item.expiryDate,
        line_discount_percentage: item?.lineDiscountType === 'Percent'
          ? Number(item?.discount || 0)
          : ((Number(item?.discount || 0) / (lineQuantity * Number.parseFloat(item.offer_price))) * 100),
        is_discount_in_percent: item?.lineDiscountType === 'Percent',
        remarks: item.remarks,
        tally_purchase_account: item.tally_purchase_account,
        production_route_line_id: item?.production_route_line_id,
        custom_fields: CustomFieldHelpers.postCfStructure(item?.lineCustomFields),
        po_line_status: item?.po_line_status?.map((item) => ({
          line_status_received_qty: Number(item?.line_status_received_qty),
          po_line_status_id: item?.po_line_status_id,
          po_line_id: item?.po_line_id
        })),
        product_batches: generateProductBatches({
          item,
          isMultipleBatchModeEnabled,
          tenantDepartmentId,
          quantity: selectedGRN.grn_entity_type === 'GOOD_RECEIVING_NOTE'
            ? Number(item.quantity)
            : Number(item.received_qty),
        }),
      };
    });
  }

  if (isFromPoScreen) {
    return data?.filter((item) => Number(item.received_qty))?.map((item) => ({
      mo_fg_id: item?.mo_fg_id,
      mo_line_id: item?.mo_line_id,
      grn_entity_line_id: item.po_line_id || null,
      is_adhoc_line: !item.po_line_id,
      tenant_product_id: item.tenant_product_id || '',
      quantity: item.received_qty ? Number(item.received_qty) : 0,
      invoice_quantity: item?.invoiceQuantity ?? Number(item?.received_qty ?? 0),
      secondary_uom_qty: item?.secondaryUomId ? item?.secondary_uom_qty : 0,
      offer_price: Number(item.offer_price) || 0,
      tax_id: item?.taxId || '',
      tax_group_info: item?.taxInfo,
      remarks: item?.remarks,
      line_discount_percentage: item?.lineDiscountType === 'Percent'
        ? Number(item?.discount || 0)
        : ((Number(item?.discount || 0) / (Number(item.received_qty) * Number.parseFloat(item.offer_price))) * 100),
      is_discount_in_percent: item?.lineDiscountType === 'Percent',
      uom_id: item?.uomId || '',
      uom_info: Array.isArray(item?.uom_info) ? item?.uom_info : (item?.uom_info ? [item?.uom_info] : []),
      expiry_date: item.expiryDate,
      tally_purchase_account: item.tally_purchase_account,
      production_route_line_id: item?.production_route_line_id,
      custom_fields: CustomFieldHelpers.postCfStructure(item?.lineCustomFields),
      product_sku_id: item?.product_sku_id,
      product_batches: generateProductBatches({
        item,
        isMultipleBatchModeEnabled,
        tenantDepartmentId,
        quantity: Number(item.received_qty),
      }),
      po_line_status: item?.po_line_status?.map((item) => ({
        line_status_received_qty: Number(item?.line_status_received_qty),
        po_line_status_id: item?.po_line_status_id,
        po_line_id: item?.po_line_id
      })),
    }));
  }

  if (isFromPoSelection) {
    return data?.filter((item) => Number(item.received_qty))?.map((item) => ({
      grn_entity_line_id: item.po_line_id || null,
      is_adhoc_line: !item.po_line_id,
      tenant_product_id: item.tenant_product_id || '',
      quantity: item.received_qty ? Number(item.received_qty) : 0,
      invoice_quantity: item?.invoiceQuantity ?? Number(item?.received_qty ?? 0),
      secondary_uom_qty: item?.secondaryUomId ? item?.secondary_uom_qty : 0,
      offer_price: Number(item.offer_price) || 0,
      remarks: item.remarks || '',
      tax_id: item?.tax_id || item?.taxId,
      tax_group_info: item?.taxInfo,
      uom_id: item?.uom_id || item?.uomId,
      uom_info: Array.isArray(item?.uom_info) ? item?.uom_info : (item?.uom_info ? [item?.uom_info] : []),
      expiry_date: item.expiryDate,
      line_discount_percentage: item?.lineDiscountType === 'Percent'
        ? Number(item?.discount || 0)
        : ((Number(item?.discount || 0) / (Number(item.received_qty) * Number.parseFloat(item.offer_price))) * 100),
      is_discount_in_percent: item?.lineDiscountType === 'Percent',
      tally_purchase_account: item.tally_purchase_account,
      production_route_line_id: item?.production_route_line_id,
      custom_fields: CustomFieldHelpers.postCfStructure(item?.lineCustomFields),
      product_batches: generateProductBatches({
        item,
        isMultipleBatchModeEnabled,
        tenantDepartmentId,
        quantity: Number(item.received_qty),
      }),
    }));
  }

  if (isAdhoc) {
    return data?.map((item) => ({
      grn_entity_line_id: item.po_line_id || null,
      is_adhoc_line: !item.po_line_id,
      tenant_product_id: item.tenant_product_id || '',
      quantity: Number(item.quantity),
      invoice_quantity: item?.invoiceQuantity ?? Number(item?.quantity),
      secondary_uom_qty: item?.secondaryUomId ? item?.secondary_uom_qty : 0,
      offer_price: Number(item.offer_price) || 0,
      remarks: item.remarks || '',
      tax_id: item?.taxId || '',
      tax_group_info: item?.taxInfo,
      uom_id: item?.uomId || '',
      uom_info: Array.isArray(item?.uom_info) ? item?.uom_info : (item?.uom_info ? [item?.uom_info] : []),
      expiry_date: item.expiryDate || null,
      line_discount_percentage: item?.lineDiscountType === 'Percent'
        ? Number(item?.discount || 0)
        : ((Number(item?.discount || 0) / (Number(item.quantity) * Number.parseFloat(item.offer_price))) * 100),
      is_discount_in_percent: item?.lineDiscountType === 'Percent',
      tally_purchase_account: item.tally_purchase_account,
      production_route_line_id: item?.production_route_line_id,
      custom_fields: CustomFieldHelpers.postCfStructure(item?.lineCustomFields),
      product_batches: generateProductBatches({
        item,
        isMultipleBatchModeEnabled,
        tenantDepartmentId,
        quantity: Number(item.quantity),
      }),
    }));
  }

  return [];
};

const generateProductBatches = (params) => {
  const { item, isMultipleBatchModeEnabled, tenantDepartmentId, quantity } = params;

  if (isMultipleBatchModeEnabled) {
    return (item.multiple_batch_info || []).map((batch) => ({
      ...batch,
      tenant_product_id: item.tenant_product_id || '',
      tenant_department_id: tenantDepartmentId,
      uom_id: item?.uomId || '',
      manufacturing_date: batch?.manufacturing_date
        ? FormHelpers.dateFormatter(batch?.manufacturing_date, item?.manufacturingDateFormat)
        : null,
      expiry_date: batch?.expiry_date
        ? FormHelpers.dateFormatter(batch?.expiry_date, item?.expiryDateFormat)
        : null,
      custom_fields: CustomFieldHelpers.postCfStructure(batch?.custom_fields),
    }));
  }

  // For non-storable and service products, don't send any batches
  if (['NON_STORABLE', 'SERVICE'].includes(item?.product_sku_info?.product_type)) {
    return [];
  }

  // For regular products, send single batch
  return [{
    ...item?.selectedBatch,
    quantity: quantity,
    tenant_product_id: item.tenant_product_id || '',
    tenant_department_id: tenantDepartmentId,
    uom_id: item?.uomId || '',
    mrp: Number(item?.selectedBatch?.mrp) || 0,
    manufacturing_date: item?.selectedBatch?.manufacturing_date
      ? FormHelpers.dateFormatter(item?.selectedBatch?.manufacturing_date, item?.manufacturingDateFormat)
      : null,
    expiry_date: item?.selectedBatch?.expiry_date
      ? FormHelpers.dateFormatter(item?.selectedBatch?.expiry_date, item?.expiryDateFormat)
      : null,
    custom_fields: CustomFieldHelpers.postCfStructure(item?.selectedBatch?.custom_fields),
  }];
};

const SelectOrdersForGRN = ({ disabled, grnTypeValue, mode, onChange, selectedPoValue, purchaseOrders, isDataMaskingPolicyEnable, isHideCostPrice, MONEY, loading, inputClassName, getPurchaseOrdersV2, selectedTenant, selectedTenantSeller }) => {

  const [searchByPONumber, setSearchByPONumber] = useState('');

  useEffect(() => {
    const handler = setTimeout(() => {
      const excludePo = grnTypeValue === 'MULTIPO';
      getPurchaseOrdersV2({
        query: {
          tenant_id: selectedTenant,
          tenant_seller_id: selectedTenantSeller,
          status: 'ISSUED',
          page: 1,
          limit: 30,
          exclude_job_works_po: excludePo,
          exclude_subcontractor_po: excludePo,
          search_keyword: searchByPONumber,
        },
      });
    }, 500); // 500ms debounce time
    return () => {
      clearTimeout(handler);
    };
  }, [searchByPONumber]);
  return (
    <PRZSelect
      getPopupContainer={(triggerNode) => triggerNode.parentNode}
      placeholder="Select purchase order.."
      labelInValue
      disabled={disabled}
      showSearch
      // allowClear
      value={selectedPoValue}
      maxTagCount="responsive"
      mode={mode}
      onChange={onChange}
      loading={loading}
      className={inputClassName}
      onSearch={(value) => setSearchByPONumber(value)}
      filterOption={false}
    >
      {purchaseOrders?.purchase_order
        ?.filter((item) => item?.closing_status !== 'CLOSED')
        ?.map((item) => (
          <Option key={item.po_id} value={item.po_id}>
            {`#${item.po_number} (${toISTDate(item.created_date || item.created_at).format(
              'DD/MM/YYYY'
            )}) - `}
            {isDataMaskingPolicyEnable && isHideCostPrice ? (
              <HideValue
                showPopOver
                popOverMessage={'You don\'t have access to view po amount'}
              />
            ) : (
              MONEY(
                item.po_grand_total,
                item?.org_currency_info?.currency_code
              )
            )}
          </Option>
        ))}
    </PRZSelect>
  );
};

export default SelectOrdersForGRN;

export const calculateCostPrice = (item, selectedPo, isApInvoiceEnabled) => {
  // If AP Invoice is enabled, cost price should be 0
  if (isApInvoiceEnabled) {
    return 0;
  }

  // If no offer price, return 0
  if (!item?.offer_price) {
    return 0;
  }

  // If conversion rate exists, apply it and round to default currency precision
  if (selectedPo?.conversion_rate) {
    const convertedPrice = item.offer_price * selectedPo.conversion_rate;
    return Number.parseFloat(convertedPrice.toFixed(DEFAULT_CUR_ROUND_OFF));
  }

  // Return the original offer price
  return item.offer_price;
};

export const getClassNameHelper = (isFlexible, isOverflow, item, formSubmitted) => {
  if (formSubmitted && (Number(item?.received_qty) <= 0)) {
    return 'orgFormInputError';
  }
  if (isFlexible) {
    return '';
  }
  if (formSubmitted && (Number(item?.quantity) - Number(item?.total_received_qty) - Number(item?.received_qty) < 0) || isOverflow) {
    return 'orgFormInputError';
  }

  return '';
};

export const getLineTotals = ({
  data,
  chargeData,
  charge1Value,
  grnTypeValue,
  taxTypeInfo,
  taxTypeName,
  freightTax,
  freightTaxData
}) => {
  data = data ?? [];
  chargeData = chargeData ?? [];
  charge1Value = charge1Value ?? 0;

  let totalAmount = 0;
  let totalDiscount = 0;
  let totalBase = 0;
  let totalTcs = 0;
  let totalTds = 0;
  let totalTax = 0;
  let totalOtherCharge = 0;
  let totalOtherChargeWithOutTax = 0;
  let grnTotal = 0;
  let totalTaxValue = 0;
  let freightCharge = Number(charge1Value);
  let totalOtherChargesForTaxableAmount = 0;
  let freightTaxAmount = 0;

  // Taxes bifurcation
  if (data?.length > 0) {
    totalTaxValue = Helpers.groupAndSumByTaxName(
      data?.map((item) => item?.child_taxes)?.flat()
    )?.reduce((acc, curr) => acc + curr?.tax_amount, 0);
  }

  for (let i = 0; i < chargeData?.length; i++) {
    let currentOtherCharge = 0;
    let currentOtherChargeWithoutTax = 0;
    let chargesTaxAmountLinesWise = 0;

    if (chargeData[i]?.charge_amount) {
      chargesTaxAmountLinesWise = Helpers.groupAndSumByTaxName(
        [...(chargeData[i]?.chargesTaxData?.child_taxes?.flat() || [])]
      )?.reduce((acc, curr) => acc + curr?.tax_amount, 0);

      currentOtherCharge = chargeData?.[i]?.chargesTax
        ? chargeData?.[i]?.charge_amount + chargesTaxAmountLinesWise
        : chargeData?.[i]?.charge_amount;

      currentOtherChargeWithoutTax = chargeData?.[i]?.charge_amount;

      if (chargeData?.[i]?.chargesTax) {
        totalOtherChargesForTaxableAmount += chargeData?.[i]?.charge_amount;
      }
    }

    totalOtherCharge += currentOtherCharge;
    totalOtherChargeWithOutTax += currentOtherChargeWithoutTax;
  }

  let lines = [];

  for (let i = 0; i < data?.length; i++) {
    let currentAmount = 0;
    let currentDiscount = 0;
    let currentBase = 0;
    let currentTax = 0;
    let currentGRN = 0;

    const quantityToUse =
      grnTypeValue === 'ADHOC' ? data[i]?.quantity : data[i]?.received_qty;

    const discountValue = Number(data[i].discount);

    if (quantityToUse) {
      currentAmount += quantityToUse * data[i].offer_price;

      if (discountValue) {
        if (data[i]?.lineDiscountType === 'Percent') {
          currentDiscount +=
            quantityToUse * data[i].offer_price * (discountValue / 100);

          currentBase =
            (quantityToUse * data[i].offer_price * (100 - discountValue)) / 100;
        } else if (data[i]?.lineDiscountType === 'Amount') {
          currentDiscount += discountValue;
          currentBase += quantityToUse * data[i].offer_price - discountValue;
        }
      } else {
        currentDiscount += 0;
        currentBase += quantityToUse * data[i].offer_price;
      }
    }

    // Subtract TDS from currentBase or Add TCS
    if (taxTypeInfo && taxTypeName === 'TCS') {
      const tcsRate = taxTypeInfo?.tax_value / 100;
      const tcsAmount = currentBase * tcsRate;
      totalTcs += tcsAmount;
    } else if (taxTypeInfo && taxTypeName === 'TDS') {
      const tdsRate = taxTypeInfo?.tax_value / 100;
      const tdsAmount = currentBase * tdsRate;
      totalTds -= tdsAmount;
    }

    if (data[i]?.taxInfo?.tax_value || data[i]?.taxInfo?.[0]?.tax_value) {
      currentTax =
        currentBase *
        ((data[i]?.taxInfo?.tax_value || data[i]?.taxInfo?.[0]?.tax_value) /
          100);
    }

    if (currentBase) currentGRN = currentBase;

    totalAmount += currentAmount;
    totalDiscount += currentDiscount;
    totalBase += currentBase;
    totalTax += currentTax;
    grnTotal += currentGRN;

    lines.push({
      grn_line_total: currentGRN + currentTax + (taxTypeName === 'TCS' ? totalTcs : totalTds),
      grn_line_quantity: quantityToUse,
      grn_line_unit_price: data[i].offer_price,
    });
  }

  grnTotal += totalTaxValue;

  lines = lines.map((line) => {
    let unitOtherCharge = 0;
    let unitFreightCharge = 0;
    const unitCostPrice = Number(line.grn_line_unit_price);

    unitOtherCharge = Number(
      ((line.grn_line_total / grnTotal) * totalOtherChargeWithOutTax) /
      line.grn_line_quantity
    );

    unitFreightCharge = Number(
      ((line.grn_line_total / grnTotal) * Number(charge1Value)) /
      line.grn_line_quantity
    );

    return {
      ...line,
      unit_landed_cost:
        Number(unitCostPrice + unitOtherCharge + unitFreightCharge).toFixed(2) ||
        0,
      unit_other_cost: Number(unitOtherCharge).toFixed(2) || 0,
      unit_freight_cost: Number(unitFreightCharge).toFixed(2) || 0,
    };
  });

  if (freightTax) {
    freightTaxAmount = Helpers.groupAndSumByTaxName(
      [...freightTaxData?.child_taxes?.flat()]
    )?.reduce((acc, curr) => acc + curr?.tax_amount, 0);

    totalBase += freightCharge;
    freightCharge += freightTaxAmount;

    if (taxTypeInfo && taxTypeName === 'TCS') {
      const tcsRate = taxTypeInfo?.tax_value / 100;
      totalTcs = totalBase * tcsRate;
    } else if (taxTypeInfo && taxTypeName === 'TDS') {
      const tdsRate = taxTypeInfo?.tax_value / 100;
      totalTds = -((totalBase + totalOtherChargesForTaxableAmount) * tdsRate);
    }
  }

  if (taxTypeInfo && taxTypeName === 'TCS') {
    const tcsRate = taxTypeInfo?.tax_value / 100;
    const tcsAmount =
      (totalBase +
        totalTaxValue +
        Number(totalOtherCharge) +
        (freightTax ? freightTaxAmount : freightCharge)) *
      tcsRate;
    totalTcs = tcsAmount;
  }

  grnTotal += taxTypeName === 'TCS' ? totalTcs : totalTds;
  grnTotal += totalOtherCharge;
  grnTotal += freightCharge;
  totalBase += totalOtherChargesForTaxableAmount;

  return {
    totalAmount,
    totalDiscount,
    totalBase,
    totalTax,
    totalTcs,
    totalTds,
    grnTotal,
    lines,
  };
};

export function isUpdateValidData({
  grnData,
  grnDate,
  ewayBillNumber,
  ewayBillList,
  purchaseAccount,
  invoiceNumber,
  selectedPo,
  selectedTenant,
  user,
  selectedPoForGrn,
  getLineTotals,
}) {
  let isValid = true;

  const isApInvoiceEnabled =
    user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.settings?.is_enabled &&
    user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.is_active;

  for (let i = 0; i < grnData?.length; i++) {
    const item = grnData[i];
    const quantity = Number(item?.quantity);
    const grnPercent = Number(item?.product_sku_info?.grn_over_flow_percent);
    const overFlowQuantity = ((grnPercent / 100) * quantity) + quantity;

    const checkOverflow = () => {
      if (user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.flexible_qty_wrt_po) {
        if (grnPercent > 0) {
          return (overFlowQuantity - item.total_received_qty - item.received_qty) < 0;
        } else if (grnPercent === 0 || grnPercent === null) {
          return (overFlowQuantity - item.total_received_qty - item.received_qty) < 0;
        }
      } else if (grnPercent > 0) {
        return (overFlowQuantity - item.total_received_qty - item.received_qty) < 0;
      } else if (grnPercent === 0 || grnPercent === null) {
        return (quantity - item.total_received_qty - item.received_qty) < 0;
      }
      return false;
    };

    if (
      checkOverflow() ||
      Number(item.received_qty) <= 0 ||
      (isApInvoiceEnabled
        ? !item?.offer_price && item?.offer_price !== 0
        : Number(item.offer_price) <= 0) ||
      !grnDate ||
      ((user?.user_tenants?.find((k) =>
        k?.tenant_id === (selectedTenant || selectedPoForGrn?.tenant_id || selectedPo?.tenant_id || user?.tenant_info?.tenant_id)
      )?.tally_connection_status &&
        (user?.tenant_info?.purchase_account_selection === 'FROM_GRN_LINE' ||
          user?.tenant_info?.purchase_account_selection === 'SAME_AS_PRODUCT_GROUP'))
        ? !item?.tally_purchase_account
        : false)
    ) {
      isValid = false;
      break;
    }

    if (item?.lineCustomFields) for (const cf of item.lineCustomFields) {
      if (cf?.isRequired && !cf?.fieldValue && cf?.fieldType?.toUpperCase() !== 'ATTACHMENT') {
        if (isApInvoiceEnabled && cf?.fieldName?.toUpperCase() === 'RATE') continue;
        isValid = false;
        break;
      }
    }
  }

  if (user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.is_e_way_bill_mandatory && getLineTotals().totalAmount >= 50_000 && user?.tenant_info?.country_code === 'IN' && (!ewayBillNumber || !ewayBillList?.length)) {
    isValid = false;
  }

  if (!purchaseAccount &&
    user?.tenant_info?.purchase_account_selection === 'FROM_GRN' &&
    !isApInvoiceEnabled) {
    isValid = false;
  }

  if (user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.grn_invoice_number_mandatory &&
    !invoiceNumber) {
    isValid = false;
  }

  return isValid;
}

export function isCreateValidData({
  grnData,
  grnDate,
  ewayBillNumber,
  ewayBillList,
  purchaseAccount,
  invoiceNumber,
  grnTypeValue,
  selectedPo,
  selectedTenant,
  selectedPoForGrn,
  user,
  getLineTotals,
  getGrnErrors
}) {
  let isValid = true;

  const isApInvoiceEnabled =
    user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.settings?.is_enabled &&
    user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.is_active;

  if (!grnDate) return false;
  if (grnTypeValue === 'ADHOC') return false;

  for (let i = 0; i < grnData?.length; i++) {
    const item = grnData[i];
    const quantity = Number(item?.quantity);
    const grnPercent = Number(item?.product_sku_info?.grn_over_flow_percent || 0);
    const overFlowQuantity = Math.floor((grnPercent / 100) * quantity + quantity);

    function checkOverflow() {
      const grnConfig = user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings;

      if (grnConfig?.flexible_qty_wrt_po) {
        if (grnPercent > 0) {
          return (overFlowQuantity - Number(item.total_received_qty) - Number(item.received_qty)) < 0;
        } else if (grnPercent === 0 || grnPercent === null) {
          return (overFlowQuantity - Number(item.total_received_qty) - Number(item.received_qty)) < 0 ? false : undefined;
        }
      } else if (grnPercent > 0) {
        return (overFlowQuantity - Number(item.total_received_qty) - Number(item.received_qty)) < 0;
      } else if (grnPercent === 0 || grnPercent === null) {
        return (quantity - Number(item.total_received_qty) - Number(item.received_qty)) < 0;
      }
    }

    if (
      checkOverflow() ||
      Number(item.received_qty) <= 0 ||
      (isApInvoiceEnabled
        ? !item?.offer_price && item?.offer_price !== 0
        : Number(item.offer_price) <= 0) ||
      !grnDate ||
      (
        user?.user_tenants?.find(
          (k) =>
            k?.tenant_id ===
            (selectedTenant ||
              selectedPoForGrn?.tenant_id ||
              selectedPo?.tenant_id)
        )?.tally_connection_status &&
          (
            user?.tenant_info?.purchase_account_selection === 'FROM_GRN_LINE' ||
            user?.tenant_info?.purchase_account_selection === 'SAME_AS_PRODUCT_GROUP'
          )
          ? !item?.tally_purchase_account
          : false
      )
    ) {
      return false;
    }

    if (item?.lineCustomFields) for (const cf of item.lineCustomFields) {
      if (cf?.isRequired && !cf?.fieldValue && cf?.fieldType?.toUpperCase() !== 'ATTACHMENT') {
        if (isApInvoiceEnabled && cf?.fieldName?.toUpperCase() === 'RATE') continue;
        return false;
      }
    }

    if (item?.product_sku_info?.product_type === 'STORABLE' && !item?.selectedBatch) {
      return false;
    }
  }

  const grnSettings = user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings;

  if (grnSettings?.is_e_way_bill_mandatory && getLineTotals().totalAmount >= 50_000 && user?.tenant_info?.country_code === 'IN' && (!ewayBillNumber || !ewayBillList?.length)) return false;

  if (!purchaseAccount && user?.tenant_info?.purchase_account_selection === 'FROM_GRN' && !isApInvoiceEnabled) {
    return false;
  }

  if (grnSettings?.grn_invoice_number_mandatory && !invoiceNumber) {
    return false;
  }

  const { docLevelError, lineLevelError } = getGrnErrors();
  if (docLevelError?.length > 0 || lineLevelError?.length > 0) {
    return false;
  }

  return isValid;
}

export function isCreateValidDataADHOC({
  grnData,
  grnDate,
  purchaseAccount,
  invoiceNumber,
  selectedSellerId,
  selectedSeller,
  grnTypeValue,
  vendorAddress,
  ewayBillNumber,
  ewayBillList,
  selectedGRN,
  selectedTenant,
  user,
  getLineTotals,
  getGrnErrors,
}) {
  let isValid = true;

  const isApInvoiceEnabled =
    user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.settings?.is_enabled &&
    user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.is_active;

  // Basic seller + vendor validation
  if ((!selectedSeller?.seller_id && !selectedSellerId) || !vendorAddress) {
    isValid = false;
  }

  if (!grnDate) return false;

  // Line-level validation
  for (let i = 0; i < grnData?.length; i++) {
    const item = grnData[i];

    if (
      Number(item.received_qty) <= 0 ||
      (isApInvoiceEnabled
        ? !item?.offer_price && item?.offer_price !== 0
        : Number(item.offer_price) <= 0) ||
      !grnDate ||
      (
        user?.user_tenants?.find((k) => k?.tenant_id === selectedTenant)
          ?.tally_connection_status &&
          (
            user?.tenant_info?.purchase_account_selection === 'FROM_GRN_LINE' ||
            user?.tenant_info?.purchase_account_selection === 'SAME_AS_PRODUCT_GROUP'
          )
          ? !item?.tally_purchase_account
          : false
      )
    ) {
      return false;
    }

    if (item?.lineCustomFields) for (const cf of item.lineCustomFields) {
      if (cf?.isRequired && !cf?.fieldValue && cf?.fieldType?.toUpperCase() !== 'ATTACHMENT') {
        if (isApInvoiceEnabled && cf?.fieldName?.toUpperCase() === 'RATE') continue;
        return false;
      }
    }

    // For storable items → batch required
    if (item?.product_sku_info?.product_type === 'STORABLE' && !item?.selectedBatch) {
      return false;
    }
  }

  // Purchase account required in some configs
  if (
    !purchaseAccount &&
    user?.tenant_info?.purchase_account_selection === 'FROM_GRN' &&
    !isApInvoiceEnabled
  ) {
    return false;
  }

  // Invoice number mandatory (for ADHOC or GRN-type entity)
  const grnSettings = user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings;
  if (
    (grnSettings?.grn_invoice_number_mandatory && !invoiceNumber) ||
    (grnSettings?.grn_invoice_number_mandatory &&
      !invoiceNumber &&
      (grnTypeValue === 'ADHOC' ||
        selectedGRN?.grn_entity_type === 'GOOD_RECEIVING_NOTE'))
  ) {
    return false;
  }

  // E-way bill mandatory for India if totalAmount >= 50k
  if (grnSettings?.is_e_way_bill_mandatory && getLineTotals().totalAmount >= 50_000 && user?.tenant_info?.country_code === 'IN' && (!ewayBillNumber || !ewayBillList?.length)) {
    return false;
  }

  // Error aggregation
  const { docLevelError, lineLevelError } = getGrnErrors();
  if (docLevelError?.length > 0 || lineLevelError?.length > 0) {
    return false;
  }

  return isValid;
}

export function isDataValid2(chargeData) {
  if (!chargeData?.length) return true;

  for (const charge of chargeData) {
    if (!charge.charge_name || !charge.charge_amount) {
      return false;
    }
  }

  return true;
}

export function evaluateDependentFields(cf, cfMap) {
  if (!cf?.dependentFields?.length) return;

  for (const dependentFieldId of cf.dependentFields) {
    const dependentField = cfMap[dependentFieldId];
    if (dependentField?.defaultExpression) {
      try {
        // inline expression evaluator
        const expression = dependentField.defaultExpression;
        const evaluatedExpression = expression.replaceAll(/{{cf_(\d+)}}/g, (match, p1) => {
          const field = cfMap[p1];
          if (!field) return 0;
          return Number(field.fieldValue || 0);
        });

        const evaluatedValue = eval(evaluatedExpression);
        dependentField.fieldValue = evaluatedValue;

        // 🔁 recursive evaluation for chained dependencies
        evaluateDependentFields(dependentField, cfMap);
      } catch (error) {
        console.error(
          `Failed to evaluate expression for field ${dependentFieldId}:`,
          error
        );
      }
    }
  }
}

export const computeUpdatedLineCFs = (
  lineCustomFields = [],
  cfId,
  fieldValue,
) => {
  const lineCfs = lineCustomFields?.map((cf) => ({ ...cf }));
  const target = lineCfs?.find((cf) => cf.cfId === cfId);
  if (!target) return lineCfs;

  target.fieldValue =
    target.fieldType === 'ATTACHMENT'
      ? transformAttachments(fieldValue)
      : fieldValue;

  const cfMap = Object.fromEntries(lineCfs?.map((cf) => [cf.cfId, cf]));
  evaluateDependentFields(target, cfMap);

  return lineCfs;
};

export const getFieldValue = (fieldName, copyDataItem, defaultValue) => {
  switch (fieldName) {
  case 'Rate': {
    return copyDataItem?.unitPrice;
  }
  case 'Quantity': {
    return copyDataItem?.quantity;
  }
  case 'Invoice Quantity': {
    return copyDataItem?.invoiceQuantity;
  }
  default: {
    return defaultValue;
  }
  }
};

export const transformAttachments = (files = []) =>
  files?.map((a) => ({
    url: a?.response?.response?.location || a?.url,
    type: a?.type,
    name: a?.name,
    uid: a?.uid,
  }));

export const createData = ({
  tenantSku, dataItem, productData, inventoryLocations, selectedSeller, isGRNLevelDiscount, discountPercentage, autoPrintDescription, discountType, cfV2DocGoodReceivingNotes, taxesGroup, selectedPoForGrn, user, billFromAddress, billToAddress,
}) => {
  const isApInvoiceEnabled = user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.settings?.is_enabled && user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.is_active;
  const lineCFs = CustomFieldHelpers.getCfStructure(cfV2DocGoodReceivingNotes.data.document_line_custom_fields?.filter((item) => item?.is_active), false);

  const isVendorOverseas = (selectedSeller?.seller_info?.seller_type === 'OVERSEAS' || selectedPoForGrn?.seller_info?.seller_type === 'OVERSEAS') && user?.tenant_info?.purchase_config?.sub_modules?.vendor?.settings?.hide_tax_for_overseas;
  const batchCustomFields = CustomFieldHelpers.getCfStructure(cfV2DocGoodReceivingNotes?.data?.batch_custom_fields, true);
  const newBatch = {
    tenant_product_id: tenantSku?.tenant_product_id,
    expiry_date: tenantSku?.product_info?.expiry_days > 0 ? (dayjs().endOf('day').add(tenantSku?.product_info?.expiry_days, 'day')) : dayjs(INFINITE_EXPIRY_DATE, 'YYYY-MM-DD').endOf('day'),
    lot_number: '',
    custom_batch_number: `${tenantSku?.product_info?.internal_sku_code}/${dayjs().format('DDMMYY')}/${tenantSku?.product_info?.product_batch_counter}`,
    cost_price: isApInvoiceEnabled ? 0 : tenantSku?.cost_price || 0,
    mrp: tenantSku?.mrp || 0,
    selling_price: tenantSku?.selling_price || 0,
    uom_id: tenantSku?.uom_id,
    manual_entry: false,
    manufacturingDateFormat: tenantSku?.manufacturing_date_format,
    expiryDateFormat: tenantSku?.expiry_date_format,
    inventory_location_id: inventoryLocations?.inventory_location?.filter((i) => i?.is_active)?.[0]?.inventory_location_id,
    inventory_location_path: inventoryLocations?.inventory_location?.filter((i) => i?.is_active)?.[0]?.inventory_location_path,
    is_rejected_batch: false,
    seller_id: selectedSeller?.seller_info?.seller_id,
    seller_name: selectedSeller?.seller_info?.seller_name,
    custom_fields: batchCustomFields,
  };
  const copyDataItem = JSON.parse(JSON.stringify(dataItem));
  copyDataItem.secondaryUomUqc = tenantSku?.secondary_uom_info?.uqc;
  copyDataItem.secondaryAvailableQty = tenantSku?.secondary_available_qty;
  copyDataItem.secondary_uom_qty = 0;
  copyDataItem.secondaryUomId = tenantSku?.secondary_uom_id;
  copyDataItem.product_sku_name = tenantSku?.product_info.product_sku_name;
  copyDataItem.product_sku_id = tenantSku?.product_info.product_sku_id;
  copyDataItem.tenant_product_id = tenantSku?.tenant_product_id;
  copyDataItem.product_type = tenantSku?.product_type;
  copyDataItem.product_sku_info = tenantSku?.product_info;
  copyDataItem.manufacturingDateFormat = tenantSku?.manufacturing_date_format;
  copyDataItem.expiryDateFormat = tenantSku?.expiry_date_format;
  copyDataItem.quantity = 1;
  copyDataItem.unitPrice = isApInvoiceEnabled ? 0 : tenantSku?.selling_price || 0;
  copyDataItem.sku = tenantSku?.product_info?.product_sku_id || '';
  copyDataItem.taxInfo = (isVendorOverseas || isApInvoiceEnabled) ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0) : tenantSku?.tax_info;
  copyDataItem.child_taxes = Helpers.computeTaxation((1 * tenantSku?.cost_price), (isVendorOverseas || isApInvoiceEnabled) ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0) : tenantSku?.tax_info, billFromAddress, billToAddress)?.tax_info?.child_taxes;
  copyDataItem.taxId = (isVendorOverseas || isApInvoiceEnabled) ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0)?.tax_id : tenantSku?.product_info?.tax_id;
  copyDataItem.uomId = tenantSku?.purchase_uom_info?.uom_id || 0;
  copyDataItem.uom_info = tenantSku?.purchase_uom_info;
  copyDataItem.uomInfo = tenantSku?.purchase_uom_info;
  copyDataItem.uomGroup = tenantSku?.group_id || tenantSku?.purchase_uom_info?.group_id;
  copyDataItem.productCategoryInfo = tenantSku?.product_category_info || {};
  copyDataItem.uom_list = tenantSku?.uom_list;
  copyDataItem.selectedBatch = newBatch;
  copyDataItem.available_batches = tenantSku?.product_batches
    ? [
      {
        ...newBatch,
      },
      ...(tenantSku?.product_batches || []).map((batch) => ({
        ...batch,
        custom_fields: CustomFieldHelpers.getCfStructure(batch?.custom_fields, true),
      })),
    ]
    : [
      {
        ...newBatch,
      },
    ];
  copyDataItem.tally_purchase_account = tenantSku?.tally_stock_group_name;
  copyDataItem.discount = isGRNLevelDiscount ? discountPercentage : 0;
  copyDataItem.nextBatchCode = `${tenantSku?.internal_sku_code}/${dayjs().format('DDMMYY')}/${tenantSku?.product_batch_counter}`;
  copyDataItem.expiryDays = tenantSku?.product_info?.expiry_days;
  copyDataItem.received_qty = 1;
  copyDataItem.remarks = autoPrintDescription ? (productData?.description || '')?.replace(/<[^>]+>/g, '') : '';
  copyDataItem.remarkRequired = !!((autoPrintDescription && (productData?.description || '')?.replace(/<[^>]+>/g, '')));
  copyDataItem.lineDiscountType = isGRNLevelDiscount ? discountType : 'Percent';
  copyDataItem.offer_price = isApInvoiceEnabled ? 0 : tenantSku?.cost_price || 0;
  copyDataItem.lineCustomFields = FormHelpers.lineSystemFieldValue(lineCFs, [...(tenantSku?.custom_fields ?? []), ...(productData?.system_custom_fields ?? [])])?.map((k) => ({
    ...k,
    fieldValue: getFieldValue(k?.fieldName, copyDataItem, k?.fieldValue),
  }));

  copyDataItem.multiple_batch_info = [{
    key: uuidv4(),
    ...newBatch,
    parentKey: copyDataItem.key,
    sq_no: 1,
    quantity: '',
    ar_number: '',
    expiryDays: tenantSku?.product_info?.expiry_days,
  }];

  return copyDataItem;
};

export const restrictMessageHelper = ({ priceMasking, grnTypeValue, isAdhocGrnAllowed }) => {
  const messages = [];

  const { isDataMaskingPolicyEnable, isHideCostPrice, isHideSellingPrice } = priceMasking || {};

  if (isDataMaskingPolicyEnable && (isHideCostPrice || isHideSellingPrice)) {
    messages.push(
      `You don't have access to view/edit${isHideCostPrice ? ' cost price' : ''}${isHideCostPrice && isHideSellingPrice ? ' and' : ''}${isHideSellingPrice ? ' selling price' : ''}.`
    );
  } else if (grnTypeValue === 'ADHOC' && isAdhocGrnAllowed) {
    messages.push(
      'You don\'t have access to create ADHOC GRN in the selected business unit.'
    );
  }

  return messages;
};

export const getGRNTableDataHelper = (data) => {
  const copyData = JSON.parse(JSON.stringify(data));
  return [...(copyData?.filter((item) => item?.product_sku_id) || []), ...(copyData?.filter((item) => !item?.product_sku_id) || [])];
};

export const splitChargesData = (charge) => {
  const chargeWithTaxName = charge?.filter((line) => line?.chargesTaxInfo?.tax_id) || [];
  const chargeWithoutTaxName = charge?.filter((line) => !line?.chargesTaxInfo?.tax_id) || [];
  return { chargeWithTaxName, chargeWithoutTaxName };
};

export const buildNewBatch = (item, po) => ({
  tenant_product_id: item?.tenant_product_id,
  expiry_date: item?.product_sku_info?.expiry_days > 0
    ? dayjs().endOf('day').add(item?.product_sku_info?.expiry_days, 'day')
    : dayjs(INFINITE_EXPIRY_DATE, 'YYYY-MM-DD').endOf('day'),
  lot_number: '',
  custom_batch_number: `${item?.product_sku_info?.internal_sku_code}/${dayjs().format('DDMMYY')}/${item?.product_sku_info?.product_batch_counter}`,
  cost_price: item?.offer_price && po?.conversion_rate
    ? (item?.offer_price * po?.conversion_rate)?.toFixed(DEFAULT_CUR_ROUND_OFF)
    : item?.offer_price || 0,
  mrp: item?.product_sku_info?.selling_price || 0,
  selling_price: 0,
  manufacturingDateFormat: item?.product_sku_info?.manufacturing_date_format,
  expiryDateFormat: item?.product_sku_info?.expiry_date_format,
  tenant_department_id: po?.tenant_department_id,
  uom_id: item?.uom_id,
  manual_entry: false,
  inventory_location_id: null,
  inventory_location_path: null,
  is_rejected_batch: false,
});

export const buildCombinedLines = ({ selectedPurchaseOrders, batchCustomFields, discountType, isVendorOverseas, taxesGroup, autoPrintDescription, cfGoodReceivingNotesLine }) => {
  const combinedLines = [];

  for (const po of selectedPurchaseOrders) {
    for (const item of po.purchase_order_lines) {
      const newBatch = { ...buildNewBatch(item, po), custom_fields: batchCustomFields };
      const parentKey = uuidv4();

      const discountValue = item?.is_discount_in_percent
        ? Number(item?.line_discount_percentage)
        : Number(item?.line_discount_amount);

      const taxableValue = discountType === 'Percent'
        ? item?.received_qty * item?.offer_price * (1 - discountValue / 100)
        : Math.max(item.received_qty * item?.offer_price - discountValue, 0);

      const lineCFs = FormHelpers.lineSystemFieldValue(
        cfGoodReceivingNotesLine,
        item?.po_line_custom_fields
      )?.map((cf) => ({
        ...cf,
        fieldValue: cf?.fieldName === 'Rate' ? item?.offer_price : cf?.fieldValue,
      }));

      combinedLines.push({
        ...item,
        source_po_id: po.po_id,
        sourcePONumber: po.po_number,
        key: parentKey,
        grn_entity_line_id: item?.po_line_id,
        received_qty: item?.received_qty,
        total_pending_qty: 0,
        secondaryUomUqc: item?.product_sku_info?.secondary_uom_info?.uqc,
        secondaryUomId: item?.product_sku_info?.secondary_uom_id,
        secondary_uom_qty: 0,
        expiryDate:
          item?.product_sku_info?.expiry_days > 0
            ? dayjs().add(item?.product_sku_info?.expiry_days || 0, 'day').format('YYYY-MM-DD')
            : INFINITE_EXPIRY_DATE,
        taxInfo: isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0) : item?.tax_info,
        child_taxes: Helpers.computeTaxation(
          taxableValue,
          isVendorOverseas ? taxesGroup?.data?.find(t => t?.tax_value === 0) : item?.tax_info,
          po?.billing_address?.state,
          po?.seller_address?.state
        )?.tax_info?.child_taxes,
        taxId: isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0)?.tax_id : item?.tax_id,
        uomId: item?.uom_id,
        uomInfo: item?.uom_info?.[0],
        remarks: autoPrintDescription ? (item?.remark || '')?.replace(/<[^>]+>/g, '') : '',
        remarkRequired: !!(autoPrintDescription && (item?.remark || '')?.replace(/<[^>]+>/g, '')),
        selectedBatch: newBatch,
        available_batches: item?.available_batches
          ? [
            newBatch,
            ...(item.available_batches || []).map(batch => ({
              ...batch,
              custom_fields: CustomFieldHelpers.getCfStructure(batch?.custom_fields, true),
            })),
          ]
          : [newBatch],
        expiryDays: item?.product_sku_info?.expiry_days,
        tally_purchase_account: item?.product_sku_info?.tally_stock_group_name,
        nextBatchCode: `${item?.product_sku_info?.internal_sku_code}/${dayjs().format('DDMMYY')}/${item?.product_sku_info?.product_batch_counter}`,
        multiple_batch_info: [{
          key: uuidv4(),
          ...newBatch,
          parentKey,
          sq_no: 1,
          quantity: '',
        }],
        discount: discountValue,
        lineDiscountType: item?.is_discount_in_percent ? 'Percent' : 'Amount',
        lineCustomFields: lineCFs,
      });
    }
  }

  return combinedLines;
};

export const mergeInventoryLocation = (lines, location) => {
  return lines.map(line => ({
    ...line,
    inventoryLocationId: location?.inventory_location_id,
    inventoryLocationPath: location?.inventory_location_path,
    selectedBatch: {
      ...line.selectedBatch,
      inventory_location_id: location?.inventory_location_id,
      inventory_location_path: location?.inventory_location_path,
    },
  }));
};

const isFieldValid = (isEnabled, isMandatory, value) => {
  // If the field is enabled and mandatory, check if the value is valid
  if (isEnabled && isMandatory) {
    // return !(value === undefined || value === '' || value === 0);
    return !(value === undefined || value === '');
  }
  // If the field is not enabled or not mandatory, it's considered valid
  return true;
};

export  function validateBatchDetailsForSingleBatch(config, batches) {
  // Helper function to check if a field is missing when it's mandatory

  // Iterate through each batch
  for (const batch of batches) {
    const batchCheck = batch?.selectedBatch;
    // If product_type is "NON_STORABLE" for the current batch, move to the next batch
    if (!['NON_STORABLE', 'SERVICE'].includes(batch?.product_sku_info?.product_type) && // Validate each mandatory field for the current batch
      (
        !isFieldValid(config?.cost_price_is_enabled, config?.cost_price_is_mandatory, batchCheck?.cost_price)
          || !isFieldValid(true, true, batchCheck?.custom_batch_number)
          || !isFieldValid(config?.lot_number_is_enabled, config?.lot_number_is_mandatory, batchCheck?.lot_number)
          || !isFieldValid(config?.selling_price_is_enabled, config?.selling_price_is_mandatory, batchCheck?.selling_price)
          || !isFieldValid(config?.mrp_is_enabled, config?.mrp_is_mandatory, batchCheck?.mrp)
          || !isFieldValid(config?.ar_number_is_enabled, config?.ar_number_is_mandatory, batchCheck?.ar_number)
          || !isFieldValid(config?.roll_no_is_enabled, config?.roll_no_is_mandatory, batchCheck?.roll_no)
          || !isFieldValid(config?.freight_cost_is_enabled, false, batchCheck?.freight_cost)
          || !isFieldValid(config?.other_cost_is_enabled, false, batchCheck?.other_cost)
          || !isFieldValid(config?.brand_is_enabled, config?.brand_is_mandatory, batchCheck?.brand)
          || !isFieldValid(config?.mfg_batch_no_is_enabled, config?.mfg_batch_no_is_mandatory, batchCheck?.mfg_batch_no)
          || !isFieldValid(config?.manufacturing_date_is_enabled, config?.manufacturing_date_is_mandatory, batchCheck?.manufacturing_date)
          // Include other conditions as needed
      )) {
      // If any field validation fails for this batch, return false
      return false;
    }
  }

  // If all batches pass validation, return true
  return true;
}

export function validateBatchDetailsForMultipleBatch(config, batches, isApInvoiceEnabled) {
  // Helper function to check if a field is missing when it's mandatory

  for (const batch of batches) {
    if (!['NON_STORABLE', 'SERVICE'].includes(batch?.product_sku_info?.product_type) && // Check if multiple_batch_info is present and is an array

      Array.isArray(batch?.multiple_batch_info)) {
      // Iterate over each item in the multiple_batch_info array

      for (const item of batch.multiple_batch_info) {
        // Validate each mandatory field for each batch item
        if (
          isApInvoiceEnabled ? false : (!isFieldValid(config?.cost_price_is_enabled, config?.cost_price_is_mandatory, item?.cost_price)
              || !isFieldValid(config?.custom_batch_number_is_enabled, config?.custom_batch_number_is_mandatory, item?.custom_batch_number)
              || !isFieldValid(config?.lot_number_is_enabled, config?.lot_number_is_mandatory, item?.lot_number)
              || isApInvoiceEnabled ? false : !isFieldValid(config?.selling_price_is_enabled, config?.selling_price_is_mandatory, item?.selling_price)
              || !isFieldValid(config?.mrp_is_enabled, config?.mrp_is_mandatory, item?.mrp)
              || !isFieldValid(config?.batch_barcode_is_enabled, config?.batch_barcode_is_mandatory, item?.batch_barcode)
              || !isFieldValid(config?.ar_number_is_enabled, config?.ar_number_is_mandatory, item?.ar_number)
              || !isFieldValid(config?.roll_no_is_enabled, config?.roll_no_is_mandatory, item?.roll_no)
              || !isFieldValid(config?.brand_is_enabled, config?.brand_is_mandatory, item?.brand)
              || !isFieldValid(config?.mfg_batch_no_is_enabled, config?.mfg_batch_no_is_mandatory, item?.mfg_batch_no)
              || !isFieldValid(config?.manufacturing_date_is_enabled, config?.manufacturing_date_is_mandatory, item?.manufacturing_date))
        // Include other conditions as needed
        ) {
          // If any field validation fails for an item, return false
          return false;
        }
      }
    }
    // Check if multiple_batch_info is present and is an array
  }

  // If all batches pass validation, return true
  return true;
}
