import {
  But<PERSON>, Spin, Table,
} from 'antd';
import React, { useEffect, useState } from 'react';
import dayjs from 'dayjs';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import './style.scss';
import H3Text from '@Uilib/h3Text';
import Helpers from '@Apis/helpers';
import { cdnUrl } from '@Utils/cdnHelper';
import H3Image from '@Uilib/h3Image';
import InvoiceActions from '@Actions/invoiceActions';

const LinkInvoice = ({
  user, getInvoice, customerId, MONEY, selectedCustomerInfo, getInvoiceLoading, callback, setInvoiceData, invoices, invoiceData
}) => {
  const [selectedRowKeys, setSelectedRowKeys] = useState(null);
  const [selectedRows, setSelectedRows] = useState(null);
  const [dataForInvTable, setDataForInvTable] = useState([]);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [limit] = useState(10);
  const [keyword, setKeyword] = useState('');

  const isInvoiceSelectable = (record) => {
    return (
      (record?.invoice_total_amount ||
        record?.invoice_grand_total * (record?.conversion_rate || 1)) -
      (record?.payment_made ||
        record?.total_payment_made * (record?.conversion_rate || 1)) -
      (record?.credits_applied * (record?.conversion_rate || 1)) >
      0
    );
  };

  useEffect(() => {
    getInvoice(
      '',
      user?.tenant_info?.tenant_id,
      '',
      currentPage,
      limit,
      customerId,
      searchKeyword,
      'CONFIRMED'
      , '', '', '', '', '', '', '', '', '', (invoiceDataFromCallback) => {
        setDataForInvTable(invoiceDataFromCallback?.data?.invoices);
        // ✅ Pre-select rows already present in invoiceData
        if (invoiceData?.length > 0) {
          const selectedKeys = invoiceData.map((inv) => inv.invoice_id);
          const selectedRecs = invoiceData.filter((inv) =>
            selectedKeys.includes(inv.invoice_id)
          );
          setSelectedRowKeys(selectedKeys);
          setSelectedRows(selectedRecs);
        }
      }
    );

  }, [customerId, currentPage, searchKeyword]);

  const getInvColumns = () => {
    const invColumns = [
      {
        title: 'INVOICE #',
        width: '120px',
        dataIndex: 'invoice_number',
        responsive: ['sm', 'md', 'lg', 'xxl'],
      },
      {
        title: 'LOCATION',
         width: '100px',
        render: (text, record) => (
          <div >
            {record?.tenant_info?.tenant_name || 'N/A'}
          </div>
        ),
        responsive: ['sm', 'md', 'lg', 'xxl'],
      },
      {
        title: 'Invoice Date',
        width: '100px',
        render: (record) => (
          <div>
            {dayjs(record?.invoice_date).format('DD/MM/YYYY')}
          </div>
        ),
        responsive: ['sm', 'md', 'lg', 'xxl'],
      },
      {
        title: 'TOTAL AMOUNT',
        width: '120px',
        render: (text, record) =>
          MONEY(
            (record?.invoice_total_amount ||
              record?.invoice_grand_total * (record?.conversion_rate || 1)) +
            record?.invoice_round_off
          ),
      },
      {
        title: 'PAYMENT STATUS',
        width: '120px',
        render: (item) => (
          <div
            className="mo__status"
            style={{ color: Helpers.getStatusColor(item.payment_status).color }}
          >
            {item.payment_status?.replaceAll('_', ' ')}
          </div>
        ),
        responsive: ['sm', 'md', 'lg', 'xxl'],
      },
      {
        title: 'Status',
        width: '100px',
        render: (item) => (
          <div className="mo__status" style={{ color: Helpers.getStatusColor(item.status).color }}>
            {Helpers.getStatusColor(item.status).text?.toUpperCase()}
          </div>
        ),
        responsive: ['sm', 'md', 'lg', 'xxl'],
      },
    ];
    return invColumns;
  };

  const getDataSource = (data) => {
    if (data) {
      // Create a deep copy of the data
      const copyData = JSON.parse(JSON.stringify(data));

      // Filter the data to include only items with mrp_id, then map it to add the key property
      return copyData?.map((item) => ({ ...item, key: item?.invoice_id })); // Map to add key property
    }
    return [];
  };
  return (
    <React.Fragment>
      <div
        className="inv__search-filters-main_container"
      >
        <div className="inv__search-filters-container">
          <div className="orgInputContainer">
            <label className="orgFormLabel">
              Selected Customer
            </label>
            <H3Text
              text={selectedCustomerInfo?.customer_name.toProperCase() || 'No Customer Selected'}
              className='customInput'
            />
          </div>

          <div className="orgInputContainer">
            <label className="orgFormLabel">
              Search Invoices
            </label>
            <div className="section-search customInput">
              <div className="doc-list__search-bar__wrapper">
                <div className="section-search-bar">
                  <input
                    value={keyword}
                    placeholder="Search for Invoice..."
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        setKeyword(e.target.value);
                        setCurrentPage(1);
                      }
                    }}
                    onChange={(event) => {
                      setKeyword(event.target.value);
                    }}
                  />
                  <H3Image
                    src={cdnUrl('icon-search.png', 'icons')}
                    className="section-search-bar__icon"
                    alt="search"
                    onClick={() => {
                      setSearchKeyword(keyword);
                      setCurrentPage(1);
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="inv__table" style={{ marginTop: '10px' }}>
          <Table
            title={() => (
              <div className="inv__table-title">
                <H3Text text={`${getDataSource(dataForInvTable)?.length || 0} Invoices`} className="" />
              </div>
            )}
            bordered
            showHeader
            size="small"
            loading={getInvoiceLoading}
            columns={getInvColumns()}
            dataSource={getDataSource(dataForInvTable) || []}
            // scroll={{ y: 'calc(100dvh - 430px)' }}// Set a fixed height for the table
            pagination={
              {
                defaultCurrent: currentPage,
                current: currentPage,
                total: invoices ? invoices?.data?.count : 0,
                pageSize: limit,
                showTotal: (tot, range) => `${range[0]}-${range[1]} of ${tot} items`,
                onChange: (page) => {
                  setCurrentPage(page);
                },
                position: ['bottomLeft'],
              }
            }
            rowSelection={
              window.screen.width > 425
                ? {
                  onChange: (rowKeys, rows) => {
                    setSelectedRowKeys(rowKeys);
                    setSelectedRows(rows);
                  },
                  selectedRowKeys,
                  getCheckboxProps: (record) => ({
                    disabled: !isInvoiceSelectable(record) ||
                      invoiceData?.some((inv) => inv?.invoice_id === record?.invoice_id),
                  }),
                }
                : false
            }
          />
        </div>
      </div>
      <div className="custom-drawer__footer" style={{ width: '100%' }}>
        <div className="ant-col-md-6">
          <Button
            type="primary"
            onClick={() => {
              if (selectedRows?.length) {
                const combinedOldAndNewLines = [
                  ...(selectedRows || []),
                  ...(invoiceData || []),
                ].filter(Boolean) // remove undefined/null
                  .filter(
                    (item, index, self) =>
                      index === self.findIndex((t) => t?.invoice_id === item?.invoice_id) // remove duplicates by invoice_id
                  );
                setInvoiceData(combinedOldAndNewLines?.map((item) => ({ ...item, payment: 0 })));
                callback();
                setDataForInvTable([]);
                setSelectedRows([]);
                setSelectedRowKeys([]);
              }
            }}
            loading={getInvoiceLoading}
            disabled={getInvoiceLoading}
          >
            Link Invoices
          </Button>
        </div>
      </div>
    </React.Fragment>
  );
};
const mapStateToProps = ({
  UserReducers, InvoiceReducers
}) => ({
  user: UserReducers.user,
  getInvoiceLoading: InvoiceReducers.getInvoiceLoading,
  MONEY: UserReducers.MONEY,
  invoices: InvoiceReducers.invoices,
});

const mapDispatchToProps = (dispatch) => ({
  getInvoice: (orgId, tenantId, invoiceId, page, limit, customerId, searchKeyword, status, startDate, endDate, departmentId, listScreen, paymentStatus, isEwayBilled, secondaryStatus, shopifySearchKeyword, tags, callback) => dispatch(InvoiceActions.getInvoice(orgId, tenantId, invoiceId, page, limit, customerId, searchKeyword, status, startDate, endDate, departmentId, listScreen, paymentStatus, isEwayBilled, secondaryStatus, shopifySearchKeyword, tags, callback)),
});

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(LinkInvoice));
