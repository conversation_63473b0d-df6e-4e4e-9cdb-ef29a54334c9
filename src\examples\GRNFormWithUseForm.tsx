import React from 'react';
import { Row, Col } from 'antd';
import { useForm } from '../hooks/useForm';
import FormFieldRenderer, { 
  textField, 
  dateField, 
  customField,
  RenderableFieldConfig 
} from '../components/Common/FormRenderer/FormFieldRenderer';

// Example: How to integrate useForm with your existing GRN form structure
const GRNFormWithUseForm: React.FC = () => {
  
  // Define GRN form fields
  const grnFieldConfigs: RenderableFieldConfig[] = [
    // Document Information
    customField('grnNumber', 'GRN Number', 'documentSequence', {
      entityName: 'GOOD_RECEIVING',
      docTitle: 'GRN#',
      updateCase: 'create' // or 'update'
    }, { required: true }),
    
    customField('selectedTenant', 'Tenant', 'tenantSelector', {
      showSearch: true
    }, { required: true }),
    
    dateField('grnDate', 'GRN Date', { 
      required: true,
      defaultValue: new Date(),
      format: 'DD/MM/YYYY'
    }),
    
    customField('sellerId', 'Vendor', 'sellerSelector', {}, { 
      required: true 
    }),
    
    // Address Information
    customField('vendorAddress', 'Vendor Address', 'addressSelector', {
      title: 'Vendor Address',
      addressType: 'VENDOR'
    }),
    
    customField('deliveryAddress', 'Delivery Address', 'addressSelector', {
      title: 'Delivery Address',
      addressType: 'DELIVERY'
    }),
    
    // Additional Fields
    textField('referenceNumber', 'Reference Number', {
      placeholder: 'Enter reference number'
    }),
    
    textField('remarks', 'Remarks', {
      type: 'textarea',
      rows: 3,
      placeholder: 'Enter any remarks...'
    })
  ];

  // Initialize form with useForm hook
  const {
    formData,
    errors,
    updateField,
    validateForm,
    getFieldProps,
    hasErrors
  } = useForm(
    // Initial data (could come from props or API)
    {
      grnDate: new Date(),
      // selectedTenant: currentTenant,
    },
    grnFieldConfigs
  );

  // Handle form submission
  const handleSubmit = async () => {
    const isValid = validateForm();
    if (!isValid) {
      console.log('Form validation failed');
      return;
    }

    try {
      console.log('Submitting GRN:', formData);
      // Your existing GRN submission logic
      // await createGRN(formData);
    } catch (error) {
      console.error('GRN submission error:', error);
    }
  };

  // Handle field changes (for any custom logic)
  const handleTenantChange = (tenantId: string) => {
    updateField('selectedTenant', tenantId);
    // Clear dependent fields when tenant changes
    updateField('sellerId', null);
    updateField('vendorAddress', null);
  };

  const handleSellerChange = (sellerId: string) => {
    updateField('sellerId', sellerId);
    // Clear address when seller changes
    updateField('vendorAddress', null);
  };

  return (
    <div className="grn-form-container">
      {/* Your existing form header/title */}
      <div className="form-header">
        <h2>Goods Receiving Note</h2>
      </div>

      {/* Document Information Section */}
      <div className="form-section">
        <h3>Document Information</h3>
        <Row gutter={16}>
          <Col span={12}>
            <FormFieldRenderer
              fieldConfig={grnFieldConfigs[0]} // grnNumber
              fieldProps={getFieldProps(grnFieldConfigs[0])}
              formData={formData}
            />
          </Col>
          <Col span={12}>
            <FormFieldRenderer
              fieldConfig={grnFieldConfigs[1]} // selectedTenant
              fieldProps={{
                ...getFieldProps(grnFieldConfigs[1]),
                onChange: handleTenantChange // Custom handler
              }}
              formData={formData}
            />
          </Col>
        </Row>
        
        <Row gutter={16} style={{ marginTop: '16px' }}>
          <Col span={12}>
            <FormFieldRenderer
              fieldConfig={grnFieldConfigs[2]} // grnDate
              fieldProps={getFieldProps(grnFieldConfigs[2])}
              formData={formData}
            />
          </Col>
          <Col span={12}>
            <FormFieldRenderer
              fieldConfig={grnFieldConfigs[3]} // sellerId
              fieldProps={{
                ...getFieldProps(grnFieldConfigs[3]),
                onChange: handleSellerChange, // Custom handler
                disabled: !formData.selectedTenant // Disable until tenant is selected
              }}
              formData={formData}
            />
          </Col>
        </Row>
      </div>

      {/* Address Information Section */}
      <div className="form-section">
        <h3>Address Information</h3>
        <Row gutter={16}>
          <Col span={12}>
            <FormFieldRenderer
              fieldConfig={grnFieldConfigs[4]} // vendorAddress
              fieldProps={{
                ...getFieldProps(grnFieldConfigs[4]),
                disabled: !formData.sellerId // Disable until seller is selected
              }}
              formData={formData}
            />
          </Col>
          <Col span={12}>
            <FormFieldRenderer
              fieldConfig={grnFieldConfigs[5]} // deliveryAddress
              fieldProps={getFieldProps(grnFieldConfigs[5])}
              formData={formData}
            />
          </Col>
        </Row>
      </div>

      {/* Additional Information Section */}
      <div className="form-section">
        <h3>Additional Information</h3>
        <Row gutter={16}>
          <Col span={12}>
            <FormFieldRenderer
              fieldConfig={grnFieldConfigs[6]} // referenceNumber
              fieldProps={getFieldProps(grnFieldConfigs[6])}
              formData={formData}
            />
          </Col>
          <Col span={12}>
            <FormFieldRenderer
              fieldConfig={grnFieldConfigs[7]} // remarks
              fieldProps={getFieldProps(grnFieldConfigs[7])}
              formData={formData}
            />
          </Col>
        </Row>
      </div>

      {/* Your existing GRN Lines component */}
      <div className="form-section">
        <h3>GRN Lines</h3>
        {/* 
          Keep your existing GRNLines component here
          <GRNLines 
            lines={formData.lines || []}
            onLinesChange={(lines) => updateField('lines', lines)}
          />
        */}
        <div style={{ 
          padding: '40px', 
          border: '2px dashed #d9d9d9', 
          textAlign: 'center',
          color: '#999'
        }}>
          Your existing GRN Lines component goes here
        </div>
      </div>

      {/* Your existing form footer */}
      <div className="form-footer">
        {/* 
          Keep your existing form footer/actions here
          <GRNFormFooter 
            onSubmit={handleSubmit}
            onCancel={handleCancel}
            hasErrors={hasErrors()}
          />
        */}
        <div style={{ 
          display: 'flex', 
          justifyContent: 'flex-end', 
          gap: '12px',
          paddingTop: '24px',
          borderTop: '1px solid #f0f0f0'
        }}>
          <button type="button" onClick={() => console.log('Cancel')}>
            Cancel
          </button>
          <button 
            type="button" 
            onClick={handleSubmit}
            disabled={hasErrors()}
            style={{ 
              backgroundColor: hasErrors() ? '#ccc' : '#1890ff',
              color: 'white',
              border: 'none',
              padding: '8px 16px',
              borderRadius: '4px'
            }}
          >
            Save GRN
          </button>
        </div>
      </div>

      {/* Debug panel (remove in production) */}
      <div style={{ 
        marginTop: '32px', 
        padding: '16px', 
        backgroundColor: '#f5f5f5',
        borderRadius: '4px'
      }}>
        <h4>Debug - Form Data:</h4>
        <pre style={{ fontSize: '12px', overflow: 'auto' }}>
          {JSON.stringify(formData, null, 2)}
        </pre>
        {Object.keys(errors).length > 0 && (
          <>
            <h4>Errors:</h4>
            <pre style={{ fontSize: '12px', color: 'red' }}>
              {JSON.stringify(errors, null, 2)}
            </pre>
          </>
        )}
      </div>
    </div>
  );
};

export default GRNFormWithUseForm;

// Example of how to create a reusable form configuration
export const createGRNFormConfig = (
  initialData: Record<string, any> = {},
  options: {
    isUpdate?: boolean;
    tenantId?: string;
    readonly?: boolean;
  } = {}
): RenderableFieldConfig[] => {
  const { isUpdate = false, tenantId, readonly = false } = options;

  return [
    customField('grnNumber', 'GRN Number', 'documentSequence', {
      entityName: 'GOOD_RECEIVING',
      docTitle: 'GRN#',
      updateCase: isUpdate ? 'update' : 'create',
      tenantId
    }, { 
      required: true,
      disabled: readonly || isUpdate 
    }),
    
    customField('selectedTenant', 'Tenant', 'tenantSelector', {
      showSearch: true
    }, { 
      required: true,
      disabled: readonly || !!tenantId,
      defaultValue: tenantId
    }),
    
    dateField('grnDate', 'GRN Date', { 
      required: true,
      defaultValue: new Date(),
      format: 'DD/MM/YYYY',
      disabled: readonly
    }),
    
    customField('sellerId', 'Vendor', 'sellerSelector', {}, { 
      required: true,
      disabled: readonly
    }),
    
    // Add more fields as needed...
  ];
};
