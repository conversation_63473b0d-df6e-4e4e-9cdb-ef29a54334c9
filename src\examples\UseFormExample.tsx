import React from 'react';
import { <PERSON><PERSON>, <PERSON>, Row, Col } from 'antd';
import { useForm } from '../hooks/useForm';
import FormFieldRenderer, { 
  textField, 
  emailField, 
  selectField, 
  dateField, 
  customField,
  RenderableFieldConfig 
} from '../components/Common/FormRenderer/FormFieldRenderer';

// Example: How to use useForm with your existing form structure
const UseFormExample: React.FC = () => {
  
  // Define your form fields configuration
  const fieldConfigs: RenderableFieldConfig[] = [
    textField('firstName', 'First Name', { 
      required: true, 
      placeholder: 'Enter first name',
      validation: [
        { type: 'required', message: 'First name is required' },
        { type: 'minLength', value: 2, message: 'First name must be at least 2 characters' }
      ]
    }),
    
    textField('lastName', 'Last Name', { 
      required: true, 
      placeholder: 'Enter last name' 
    }),
    
    emailField('email', 'Email Address', { 
      required: true, 
      placeholder: 'Enter email address',
      validation: [
        { type: 'required', message: 'Email is required' },
        { type: 'email', message: 'Please enter a valid email address' }
      ]
    }),
    
    selectField('department', 'Department', [
      { label: 'Sales', value: 'sales' },
      { label: 'Marketing', value: 'marketing' },
      { label: 'Engineering', value: 'engineering' },
      { label: 'HR', value: 'hr' }
    ], { required: true }),
    
    dateField('startDate', 'Start Date', { 
      required: true,
      format: 'DD/MM/YYYY'
    }),
    
    // Example of custom components
    customField('selectedTenant', 'Tenant', 'tenantSelector', {
      showSearch: true
    }, { required: true }),
    
    customField('grnNumber', 'GRN Number', 'documentSequence', {
      entityName: 'GOOD_RECEIVING',
      docTitle: 'GRN#'
    }, { required: true }),
    
    customField('sellerId', 'Vendor', 'sellerSelector', {}, { 
      required: true 
    })
  ];

  // Initialize the form hook
  const {
    formData,
    errors,
    touched,
    updateField,
    validateForm,
    hasErrors,
    getFieldProps,
    resetForm
  } = useForm(
    // Initial data
    {
      firstName: 'John',
      email: '<EMAIL>'
    },
    // Field configurations
    fieldConfigs
  );

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const isValid = validateForm();
    if (!isValid) {
      console.log('Form validation failed');
      return;
    }

    try {
      console.log('Submitting form data:', formData);
      // Your API call here
      // await submitFormData(formData);
      alert('Form submitted successfully!');
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  const handleReset = () => {
    resetForm();
  };

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <h1>useForm Hook Example</h1>
      
      <Row gutter={24}>
        {/* Form Column */}
        <Col span={16}>
          <Card title="Form Example" style={{ marginBottom: '24px' }}>
            <form onSubmit={handleSubmit}>
              
              {/* Basic Information Section */}
              <div style={{ marginBottom: '32px' }}>
                <h3>Basic Information</h3>
                <Row gutter={16}>
                  <Col span={12}>
                    <FormFieldRenderer
                      fieldConfig={fieldConfigs[0]} // firstName
                      fieldProps={getFieldProps(fieldConfigs[0])}
                    />
                  </Col>
                  <Col span={12}>
                    <FormFieldRenderer
                      fieldConfig={fieldConfigs[1]} // lastName
                      fieldProps={getFieldProps(fieldConfigs[1])}
                    />
                  </Col>
                </Row>
                
                <Row gutter={16} style={{ marginTop: '16px' }}>
                  <Col span={12}>
                    <FormFieldRenderer
                      fieldConfig={fieldConfigs[2]} // email
                      fieldProps={getFieldProps(fieldConfigs[2])}
                    />
                  </Col>
                  <Col span={12}>
                    <FormFieldRenderer
                      fieldConfig={fieldConfigs[3]} // department
                      fieldProps={getFieldProps(fieldConfigs[3])}
                    />
                  </Col>
                </Row>
              </div>

              {/* Work Information Section */}
              <div style={{ marginBottom: '32px' }}>
                <h3>Work Information</h3>
                <Row gutter={16}>
                  <Col span={12}>
                    <FormFieldRenderer
                      fieldConfig={fieldConfigs[4]} // startDate
                      fieldProps={getFieldProps(fieldConfigs[4])}
                    />
                  </Col>
                  <Col span={12}>
                    <FormFieldRenderer
                      fieldConfig={fieldConfigs[5]} // selectedTenant
                      fieldProps={getFieldProps(fieldConfigs[5])}
                      formData={formData}
                    />
                  </Col>
                </Row>
              </div>

              {/* Document Information Section */}
              <div style={{ marginBottom: '32px' }}>
                <h3>Document Information</h3>
                <Row gutter={16}>
                  <Col span={12}>
                    <FormFieldRenderer
                      fieldConfig={fieldConfigs[6]} // grnNumber
                      fieldProps={getFieldProps(fieldConfigs[6])}
                      formData={formData}
                    />
                  </Col>
                  <Col span={12}>
                    <FormFieldRenderer
                      fieldConfig={fieldConfigs[7]} // sellerId
                      fieldProps={getFieldProps(fieldConfigs[7])}
                      formData={formData}
                    />
                  </Col>
                </Row>
              </div>

              {/* Form Actions */}
              <div style={{ 
                display: 'flex', 
                gap: '12px', 
                justifyContent: 'flex-end',
                paddingTop: '24px',
                borderTop: '1px solid #f0f0f0'
              }}>
                <Button type="default" onClick={handleReset}>
                  Reset
                </Button>
                <Button 
                  type="primary" 
                  htmlType="submit"
                  disabled={hasErrors()}
                >
                  Submit
                </Button>
              </div>
            </form>
          </Card>
        </Col>

        {/* Debug Column */}
        <Col span={8}>
          <Card title="Form State" style={{ marginBottom: '16px' }}>
            <div>
              <h4>Form Data:</h4>
              <pre style={{ 
                background: '#f5f5f5', 
                padding: '12px', 
                borderRadius: '4px',
                fontSize: '12px',
                overflow: 'auto',
                maxHeight: '200px'
              }}>
                {JSON.stringify(formData, null, 2)}
              </pre>
            </div>
          </Card>

          <Card title="Errors" style={{ marginBottom: '16px' }}>
            <div>
              <pre style={{ 
                background: '#fff2f0', 
                padding: '12px', 
                borderRadius: '4px',
                fontSize: '12px',
                overflow: 'auto',
                maxHeight: '150px'
              }}>
                {JSON.stringify(errors, null, 2)}
              </pre>
            </div>
          </Card>

          <Card title="Touched Fields">
            <div>
              <pre style={{ 
                background: '#f0f9ff', 
                padding: '12px', 
                borderRadius: '4px',
                fontSize: '12px',
                overflow: 'auto',
                maxHeight: '150px'
              }}>
                {JSON.stringify(touched, null, 2)}
              </pre>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Usage Instructions */}
      <Card title="How to Use useForm Hook" style={{ marginTop: '24px' }}>
        <div>
          <h4>1. Define Field Configurations:</h4>
          <pre style={{ background: '#f5f5f5', padding: '12px', borderRadius: '4px', fontSize: '12px' }}>
{`const fieldConfigs = [
  textField('firstName', 'First Name', { required: true }),
  emailField('email', 'Email', { required: true }),
  customField('tenant', 'Tenant', 'tenantSelector', { showSearch: true })
];`}
          </pre>

          <h4>2. Initialize the Hook:</h4>
          <pre style={{ background: '#f5f5f5', padding: '12px', borderRadius: '4px', fontSize: '12px' }}>
{`const { formData, getFieldProps, validateForm } = useForm(initialData, fieldConfigs);`}
          </pre>

          <h4>3. Render Fields:</h4>
          <pre style={{ background: '#f5f5f5', padding: '12px', borderRadius: '4px', fontSize: '12px' }}>
{`<FormFieldRenderer
  fieldConfig={fieldConfig}
  fieldProps={getFieldProps(fieldConfig)}
  formData={formData}
/>`}
          </pre>
        </div>
      </Card>
    </div>
  );
};

export default UseFormExample;
