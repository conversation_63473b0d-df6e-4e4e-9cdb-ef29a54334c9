import { useState, useCallback, useMemo } from 'react';

// Simple validation rule interface
export interface ValidationRule {
  type: 'required' | 'email' | 'min' | 'max' | 'minLength' | 'maxLength' | 'pattern' | 'custom';
  value?: any;
  message: string;
  validator?: (value: any, formData: any) => boolean | string;
}

// Field configuration interface
export interface FormFieldConfig {
  id: string;
  label: string;
  required?: boolean;
  disabled?: boolean;
  validation?: ValidationRule[];
  defaultValue?: any;
}

// Hook return interface
export interface UseFormReturn {
  // Form data and state
  formData: Record<string, any>;
  errors: Record<string, string>;
  touched: Record<string, boolean>;
  
  // Field operations
  updateField: (fieldId: string, value: any) => void;
  updateMultipleFields: (updates: Record<string, any>) => void;
  setFormData: (data: Record<string, any>) => void;
  
  // Validation
  validateField: (fieldId: string) => boolean;
  validateForm: () => boolean;
  setFieldError: (fieldId: string, error: string) => void;
  clearFieldError: (fieldId: string) => void;
  
  // Utilities
  getFieldValue: (fieldId: string) => any;
  isFieldTouched: (fieldId: string) => boolean;
  hasErrors: () => boolean;
  resetForm: () => void;
  
  // Field rendering helpers
  getFieldProps: (fieldConfig: FormFieldConfig) => FieldProps;
  renderField: (fieldConfig: FormFieldConfig) => React.ReactElement;
}

// Props that will be passed to form components
export interface FieldProps {
  value: any;
  onChange: (value: any) => void;
  error?: string;
  required?: boolean;
  disabled?: boolean;
  name: string;
  label: string;
  placeholder?: string;
  containerClassName?: string;
  formSubmitted?: boolean;
}

export const useForm = (
  initialData: Record<string, any> = {},
  fieldConfigs: FormFieldConfig[] = []
): UseFormReturn => {
  
  // Initialize form data with default values from field configs
  const getInitialData = useMemo(() => {
    const defaultData: Record<string, any> = {};
    fieldConfigs.forEach(config => {
      if (config.defaultValue !== undefined) {
        defaultData[config.id] = config.defaultValue;
      }
    });
    return { ...defaultData, ...initialData };
  }, [initialData, fieldConfigs]);

  // Form state
  const [formData, setFormDataState] = useState<Record<string, any>>(getInitialData);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  // Get field config by ID
  const getFieldConfig = useCallback((fieldId: string): FormFieldConfig | undefined => {
    return fieldConfigs.find(config => config.id === fieldId);
  }, [fieldConfigs]);

  // Validate a single field
  const validateField = useCallback((fieldId: string): boolean => {
    const config = getFieldConfig(fieldId);
    if (!config) return true;

    const value = formData[fieldId];
    const fieldErrors: string[] = [];

    // Required validation
    if (config.required && (value === undefined || value === null || value === '')) {
      fieldErrors.push(`${config.label} is required`);
    }

    // Custom validation rules
    if (config.validation && value !== undefined && value !== null && value !== '') {
      config.validation.forEach((rule: ValidationRule) => {
        let isValid = true;
        let errorMessage = rule.message;

        switch (rule.type) {
          case 'email':
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            isValid = emailRegex.test(value);
            break;
          
          case 'min':
            isValid = Number(value) >= rule.value;
            break;
          
          case 'max':
            isValid = Number(value) <= rule.value;
            break;
          
          case 'minLength':
            isValid = String(value).length >= rule.value;
            break;
          
          case 'maxLength':
            isValid = String(value).length <= rule.value;
            break;
          
          case 'pattern':
            const regex = new RegExp(rule.value);
            isValid = regex.test(value);
            break;
          
          case 'custom':
            if (rule.validator) {
              const result = rule.validator(value, formData);
              if (typeof result === 'string') {
                isValid = false;
                errorMessage = result;
              } else {
                isValid = result;
              }
            }
            break;
        }

        if (!isValid) {
          fieldErrors.push(errorMessage);
        }
      });
    }

    // Update errors state
    if (fieldErrors.length > 0) {
      setErrors(prev => ({ ...prev, [fieldId]: fieldErrors[0] }));
      return false;
    } else {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[fieldId];
        return newErrors;
      });
      return true;
    }
  }, [formData, getFieldConfig]);

  // Validate entire form
  const validateForm = useCallback((): boolean => {
    let isValid = true;
    
    fieldConfigs.forEach(config => {
      const fieldValid = validateField(config.id);
      if (!fieldValid) {
        isValid = false;
      }
    });

    return isValid;
  }, [fieldConfigs, validateField]);

  // Update single field
  const updateField = useCallback((fieldId: string, value: any) => {
    setFormDataState(prev => ({ ...prev, [fieldId]: value }));
    setTouched(prev => ({ ...prev, [fieldId]: true }));
    
    // Clear error when user starts typing
    if (errors[fieldId]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[fieldId];
        return newErrors;
      });
    }
  }, [errors]);

  // Update multiple fields at once
  const updateMultipleFields = useCallback((updates: Record<string, any>) => {
    setFormDataState(prev => ({ ...prev, ...updates }));
    
    // Mark all updated fields as touched
    const touchedUpdates: Record<string, boolean> = {};
    Object.keys(updates).forEach(fieldId => {
      touchedUpdates[fieldId] = true;
    });
    setTouched(prev => ({ ...prev, ...touchedUpdates }));
  }, []);

  // Set form data (replace entire form data)
  const setFormData = useCallback((data: Record<string, any>) => {
    setFormDataState(data);
  }, []);

  // Set field error manually
  const setFieldError = useCallback((fieldId: string, error: string) => {
    setErrors(prev => ({ ...prev, [fieldId]: error }));
  }, []);

  // Clear field error
  const clearFieldError = useCallback((fieldId: string) => {
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[fieldId];
      return newErrors;
    });
  }, []);

  // Get field value
  const getFieldValue = useCallback((fieldId: string) => {
    return formData[fieldId];
  }, [formData]);

  // Check if field is touched
  const isFieldTouched = useCallback((fieldId: string) => {
    return touched[fieldId] || false;
  }, [touched]);

  // Check if form has any errors
  const hasErrors = useCallback(() => {
    return Object.keys(errors).length > 0;
  }, [errors]);

  // Reset form to initial state
  const resetForm = useCallback(() => {
    setFormDataState(getInitialData);
    setErrors({});
    setTouched({});
  }, [getInitialData]);

  // Get props for a field (to be spread into your existing components)
  const getFieldProps = useCallback((fieldConfig: FormFieldConfig): FieldProps => {
    const value = formData[fieldConfig.id];
    const error = errors[fieldConfig.id];
    const isRequired = fieldConfig.required || false;
    const isDisabled = fieldConfig.disabled || false;

    return {
      value: value || '',
      onChange: (newValue: any) => updateField(fieldConfig.id, newValue),
      error,
      required: isRequired,
      disabled: isDisabled,
      name: fieldConfig.id,
      label: fieldConfig.label,
      containerClassName: error ? 'form-error__input' : '',
      formSubmitted: touched[fieldConfig.id] && !!error,
    };
  }, [formData, errors, touched, updateField]);

  // Render field helper (you can customize this based on your needs)
  const renderField = useCallback((fieldConfig: FormFieldConfig) => {
    // This is a placeholder - you'll implement this based on your component library
    const props = getFieldProps(fieldConfig);
    
    // You can return your specific components here based on field type
    // For now, this is just a placeholder
    return null;
  }, [getFieldProps]);

  return {
    // State
    formData,
    errors,
    touched,
    
    // Field operations
    updateField,
    updateMultipleFields,
    setFormData,
    
    // Validation
    validateField,
    validateForm,
    setFieldError,
    clearFieldError,
    
    // Utilities
    getFieldValue,
    isFieldTouched,
    hasErrors,
    resetForm,
    
    // Field rendering helpers
    getFieldProps,
    renderField,
  };
};
