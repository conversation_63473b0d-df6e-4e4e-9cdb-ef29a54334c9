import { useState, useCallback, useEffect, useMemo } from 'react';
import { 
  FormSchema, 
  FieldSchema, 
  FormRendererHookReturn, 
  ValidationRule,
  FieldDependency 
} from '../types/FormSchema';

export const useFormRenderer = (
  schema: FormSchema, 
  initialData: Record<string, any> = {}
): FormRendererHookReturn => {
  
  // Form state
  const [formData, setFormData] = useState<Record<string, any>>(initialData);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});
  const [loading, setLoading] = useState(false);

  // Initialize form data with default values from schema
  useEffect(() => {
    const defaultData: Record<string, any> = {};
    
    schema.sections.forEach(section => {
      section.fields.forEach(field => {
        if (field.defaultValue !== undefined && formData[field.id] === undefined) {
          defaultData[field.id] = field.defaultValue;
        }
      });
    });

    if (Object.keys(defaultData).length > 0) {
      setFormData(prev => ({ ...defaultData, ...prev }));
    }
  }, [schema]);

  // Get all fields from schema (flattened)
  const allFields = useMemo(() => {
    const fields: FieldSchema[] = [];
    schema.sections.forEach(section => {
      fields.push(...section.fields);
    });
    return fields;
  }, [schema]);

  // Field validation function
  const validateField = useCallback((fieldId: string): boolean => {
    const field = allFields.find(f => f.id === fieldId);
    if (!field) return true;

    const value = formData[fieldId];
    const fieldErrors: string[] = [];

    // Required validation
    if (field.required && (value === undefined || value === null || value === '')) {
      fieldErrors.push(`${field.label} is required`);
    }

    // Custom validation rules
    if (field.validation && value !== undefined && value !== null && value !== '') {
      field.validation.forEach((rule: ValidationRule) => {
        let isValid = true;
        let errorMessage = rule.message;

        switch (rule.type) {
          case 'email':
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            isValid = emailRegex.test(value);
            break;
          
          case 'min':
            isValid = Number(value) >= rule.value;
            break;
          
          case 'max':
            isValid = Number(value) <= rule.value;
            break;
          
          case 'minLength':
            isValid = String(value).length >= rule.value;
            break;
          
          case 'maxLength':
            isValid = String(value).length <= rule.value;
            break;
          
          case 'pattern':
            const regex = new RegExp(rule.value);
            isValid = regex.test(value);
            break;
          
          case 'custom':
            if (rule.validator) {
              const result = rule.validator(value, formData);
              if (typeof result === 'string') {
                isValid = false;
                errorMessage = result;
              } else {
                isValid = result;
              }
            }
            break;
        }

        if (!isValid) {
          fieldErrors.push(errorMessage);
        }
      });
    }

    // Update errors state
    if (fieldErrors.length > 0) {
      setErrors(prev => ({ ...prev, [fieldId]: fieldErrors[0] }));
      return false;
    } else {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[fieldId];
        return newErrors;
      });
      return true;
    }
  }, [formData, allFields]);

  // Form validation function
  const validateForm = useCallback((): boolean => {
    let isValid = true;
    const newErrors: Record<string, string> = {};

    // Validate all visible and enabled fields
    allFields.forEach(field => {
      if (isFieldVisible(field.id) && !isFieldDisabled(field.id)) {
        const fieldValid = validateField(field.id);
        if (!fieldValid) {
          isValid = false;
        }
      }
    });

    // Cross-field validation
    if (schema.validation?.crossFieldValidation) {
      const crossFieldErrors = schema.validation.crossFieldValidation(formData);
      Object.entries(crossFieldErrors).forEach(([fieldId, error]) => {
        newErrors[fieldId] = error;
        isValid = false;
      });
      setErrors(prev => ({ ...prev, ...newErrors }));
    }

    return isValid;
  }, [formData, allFields, schema.validation]);

  // Check if field should be visible based on dependencies
  const isFieldVisible = useCallback((fieldId: string): boolean => {
    const field = allFields.find(f => f.id === fieldId);
    if (!field || field.visible === false) return false;

    if (!field.dependencies) return true;

    return field.dependencies.every((dep: FieldDependency) => {
      const depValue = formData[dep.field];
      let conditionMet = false;

      switch (dep.condition) {
        case 'hasValue':
          conditionMet = depValue !== undefined && depValue !== null && depValue !== '';
          break;
        case 'equals':
          conditionMet = depValue === dep.value;
          break;
        case 'notEquals':
          conditionMet = depValue !== dep.value;
          break;
        case 'greaterThan':
          conditionMet = Number(depValue) > dep.value;
          break;
        case 'lessThan':
          conditionMet = Number(depValue) < dep.value;
          break;
        case 'contains':
          conditionMet = Array.isArray(depValue) ? depValue.includes(dep.value) : String(depValue).includes(dep.value);
          break;
        case 'custom':
          conditionMet = dep.customCondition ? dep.customCondition(depValue, formData) : true;
          break;
      }

      // Apply action based on condition
      if (conditionMet) {
        return dep.action === 'show' || dep.action === 'enable' || dep.action === 'require';
      } else {
        return dep.action === 'hide' || dep.action === 'disable' || dep.action === 'optional';
      }
    });
  }, [formData, allFields]);

  // Check if field should be disabled based on dependencies
  const isFieldDisabled = useCallback((fieldId: string): boolean => {
    const field = allFields.find(f => f.id === fieldId);
    if (!field) return false;
    if (field.disabled) return true;

    if (!field.dependencies) return false;

    return field.dependencies.some((dep: FieldDependency) => {
      const depValue = formData[dep.field];
      let conditionMet = false;

      switch (dep.condition) {
        case 'hasValue':
          conditionMet = depValue !== undefined && depValue !== null && depValue !== '';
          break;
        case 'equals':
          conditionMet = depValue === dep.value;
          break;
        case 'notEquals':
          conditionMet = depValue !== dep.value;
          break;
        case 'custom':
          conditionMet = dep.customCondition ? dep.customCondition(depValue, formData) : false;
          break;
      }

      return conditionMet && dep.action === 'disable';
    });
  }, [formData, allFields]);

  // Check if field is required (including dynamic requirements)
  const isFieldRequired = useCallback((fieldId: string): boolean => {
    const field = allFields.find(f => f.id === fieldId);
    if (!field) return false;

    let isRequired = field.required || false;

    // Check dependencies for dynamic requirements
    if (field.dependencies) {
      field.dependencies.forEach((dep: FieldDependency) => {
        const depValue = formData[dep.field];
        let conditionMet = false;

        switch (dep.condition) {
          case 'hasValue':
            conditionMet = depValue !== undefined && depValue !== null && depValue !== '';
            break;
          case 'equals':
            conditionMet = depValue === dep.value;
            break;
          case 'custom':
            conditionMet = dep.customCondition ? dep.customCondition(depValue, formData) : false;
            break;
        }

        if (conditionMet && dep.action === 'require') {
          isRequired = true;
        } else if (conditionMet && dep.action === 'optional') {
          isRequired = false;
        }
      });
    }

    return isRequired;
  }, [formData, allFields]);

  // Update single field
  const updateField = useCallback((fieldId: string, value: any) => {
    setFormData(prev => ({ ...prev, [fieldId]: value }));
    setTouched(prev => ({ ...prev, [fieldId]: true }));
    
    // Clear error when user starts typing
    if (errors[fieldId]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[fieldId];
        return newErrors;
      });
    }

    // Trigger onChange validation if configured
    if (schema.validation?.onChange) {
      const error = schema.validation.onChange(fieldId, value, { ...formData, [fieldId]: value });
      if (error && typeof error === 'string') {
        setErrors(prev => ({ ...prev, [fieldId]: error }));
      }
    }
  }, [formData, errors, schema.validation]);

  // Update multiple fields at once
  const updateMultipleFields = useCallback((updates: Record<string, any>) => {
    setFormData(prev => ({ ...prev, ...updates }));
    
    // Mark all updated fields as touched
    const touchedUpdates: Record<string, boolean> = {};
    Object.keys(updates).forEach(fieldId => {
      touchedUpdates[fieldId] = true;
    });
    setTouched(prev => ({ ...prev, ...touchedUpdates }));
  }, []);

  // Reset form to initial state
  const resetForm = useCallback(() => {
    setFormData(initialData);
    setErrors({});
    setTouched({});
  }, [initialData]);

  // Set field error manually
  const setFieldError = useCallback((fieldId: string, error: string) => {
    setErrors(prev => ({ ...prev, [fieldId]: error }));
  }, []);

  // Clear field error
  const clearFieldError = useCallback((fieldId: string) => {
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[fieldId];
      return newErrors;
    });
  }, []);

  // Get field value
  const getFieldValue = useCallback((fieldId: string) => {
    return formData[fieldId];
  }, [formData]);

  return {
    formData,
    errors,
    touched,
    loading,
    updateField,
    updateMultipleFields,
    validateForm,
    validateField,
    resetForm,
    setFormData,
    setFieldError,
    clearFieldError,
    getFieldValue,
    isFieldVisible,
    isFieldDisabled,
    isFieldRequired,
  };
};
