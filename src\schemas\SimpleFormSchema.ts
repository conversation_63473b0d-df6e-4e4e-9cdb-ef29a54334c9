import { FormSchema } from '../types/FormSchema';

export const SimpleFormSchema: FormSchema = {
  id: 'simple-form',
  title: 'Simple Form Example',
  description: 'A basic form to test the form renderer',
  sections: [
    {
      id: 'basic-info',
      title: 'Basic Information',
      layout: { columns: 2 },
      fields: [
        {
          id: 'firstName',
          type: 'text',
          label: 'First Name',
          required: true,
          placeholder: 'Enter your first name',
          validation: [
            { type: 'required', message: 'First name is required' },
            { type: 'minLength', value: 2, message: 'First name must be at least 2 characters' }
          ]
        },
        {
          id: 'lastName',
          type: 'text',
          label: 'Last Name',
          required: true,
          placeholder: 'Enter your last name',
          validation: [
            { type: 'required', message: 'Last name is required' }
          ]
        },
        {
          id: 'email',
          type: 'email',
          label: 'Email Address',
          required: true,
          placeholder: 'Enter your email',
          validation: [
            { type: 'required', message: 'Email is required' },
            { type: 'email', message: 'Please enter a valid email address' }
          ]
        },
        {
          id: 'phone',
          type: 'text',
          label: 'Phone Number',
          placeholder: 'Enter your phone number',
          validation: [
            { type: 'pattern', value: '^[0-9+\\-\\s()]+$', message: 'Please enter a valid phone number' }
          ]
        }
      ]
    },
    {
      id: 'preferences',
      title: 'Preferences',
      fields: [
        {
          id: 'department',
          type: 'select',
          label: 'Department',
          required: true,
          placeholder: 'Select your department',
          options: [
            { label: 'Sales', value: 'sales' },
            { label: 'Marketing', value: 'marketing' },
            { label: 'Engineering', value: 'engineering' },
            { label: 'HR', value: 'hr' }
          ]
        },
        {
          id: 'experience',
          type: 'number',
          label: 'Years of Experience',
          placeholder: 'Enter years of experience',
          validation: [
            { type: 'min', value: 0, message: 'Experience cannot be negative' },
            { type: 'max', value: 50, message: 'Experience cannot exceed 50 years' }
          ]
        },
        {
          id: 'startDate',
          type: 'date',
          label: 'Start Date',
          required: true,
          dependencies: [
            {
              field: 'department',
              condition: 'hasValue',
              action: 'show'
            }
          ]
        },
        {
          id: 'isManager',
          type: 'checkbox',
          label: 'Manager Role',
          checkboxText: 'I am in a management position',
          dependencies: [
            {
              field: 'experience',
              condition: 'greaterThan',
              value: 2,
              action: 'show'
            }
          ]
        }
      ]
    },
    {
      id: 'additional-info',
      title: 'Additional Information',
      collapsible: true,
      defaultCollapsed: true,
      fields: [
        {
          id: 'bio',
          type: 'textarea',
          label: 'Bio',
          placeholder: 'Tell us about yourself...',
          rows: 4,
          validation: [
            { type: 'maxLength', value: 500, message: 'Bio cannot exceed 500 characters' }
          ]
        },
        {
          id: 'skills',
          type: 'multiselect',
          label: 'Skills',
          placeholder: 'Select your skills',
          mode: 'multiple',
          options: [
            { label: 'JavaScript', value: 'javascript' },
            { label: 'React', value: 'react' },
            { label: 'Node.js', value: 'nodejs' },
            { label: 'Python', value: 'python' },
            { label: 'SQL', value: 'sql' },
            { label: 'Project Management', value: 'pm' }
          ]
        }
      ]
    }
  ],
  actions: {
    submit: {
      text: 'Save User',
      className: 'submit-btn'
    },
    cancel: {
      text: 'Cancel',
      className: 'cancel-btn'
    },
    reset: {
      text: 'Reset Form',
      className: 'reset-btn'
    }
  },
  validation: {
    crossFieldValidation: (formData) => {
      const errors: Record<string, string> = {};
      
      // Example: If manager role is selected, experience should be > 2
      if (formData.isManager && (!formData.experience || formData.experience <= 2)) {
        errors.experience = 'Managers must have more than 2 years of experience';
      }
      
      return errors;
    }
  }
};

// Example of a more complex form with custom components
export const GRNFormSchemaExample: FormSchema = {
  id: 'grn-form-example',
  title: 'Goods Receiving Note',
  description: 'Create or update a goods receiving note',
  sections: [
    {
      id: 'document-info',
      title: 'Document Information',
      layout: { columns: 2 },
      fields: [
        {
          id: 'grnNumber',
          type: 'custom',
          component: 'documentSequence',
          label: 'GRN Number',
          required: true,
          props: {
            entityName: 'GOOD_RECEIVING',
            docTitle: 'GRN#',
          }
        },
        {
          id: 'selectedTenant',
          type: 'custom',
          component: 'tenantSelector',
          label: 'Tenant',
          required: true,
          props: {
            showSearch: true,
          }
        },
        {
          id: 'grnDate',
          type: 'date',
          label: 'GRN Date',
          required: true,
          defaultValue: new Date(),
        },
        {
          id: 'sellerId',
          type: 'custom',
          component: 'sellerSelector',
          label: 'Vendor',
          required: true,
          dependencies: [
            {
              field: 'selectedTenant',
              condition: 'hasValue',
              action: 'enable'
            }
          ]
        }
      ]
    },
    {
      id: 'address-info',
      title: 'Address Information',
      fields: [
        {
          id: 'vendorAddress',
          type: 'custom',
          component: 'addressSelector',
          label: 'Vendor Address',
          props: {
            title: 'Vendor Address',
            addressType: 'VENDOR'
          },
          dependencies: [
            {
              field: 'sellerId',
              condition: 'hasValue',
              action: 'show'
            }
          ]
        }
      ]
    }
  ],
  actions: {
    submit: {
      text: 'Save GRN',
    },
    cancel: {
      text: 'Cancel',
    }
  }
};
