// Base types for form schema engine
export type FieldType = 
  | 'text' 
  | 'email' 
  | 'password' 
  | 'textarea'
  | 'number' 
  | 'currency'
  | 'date'
  | 'datetime'
  | 'select' 
  | 'multiselect'
  | 'checkbox'
  | 'radio'
  | 'custom';

export type ValidationRuleType = 
  | 'required' 
  | 'email' 
  | 'min' 
  | 'max' 
  | 'minLength' 
  | 'maxLength'
  | 'pattern'
  | 'custom';

export type DependencyCondition = 
  | 'hasValue' 
  | 'equals' 
  | 'notEquals' 
  | 'greaterThan' 
  | 'lessThan'
  | 'contains'
  | 'custom';

export type DependencyAction = 
  | 'show' 
  | 'hide' 
  | 'enable' 
  | 'disable' 
  | 'require' 
  | 'optional'
  | 'updateOptions';

export interface ValidationRule {
  type: ValidationRuleType;
  value?: any;
  message: string;
  validator?: (value: any, formData: any) => boolean | string;
}

export interface FieldDependency {
  field: string;
  condition: DependencyCondition;
  value?: any;
  action: DependencyAction;
  customCondition?: (fieldValue: any, formData: any) => boolean;
  customAction?: (formData: any) => any;
}

export interface SelectOption {
  label: string;
  value: any;
  disabled?: boolean;
  data?: any; // Additional data for the option
}

export interface FormLayout {
  columns?: number;
  gap?: string;
  direction?: 'row' | 'column';
  className?: string;
}

// Base field schema interface
export interface BaseFieldSchema {
  id: string;
  type: FieldType;
  label: string;
  required?: boolean;
  disabled?: boolean;
  visible?: boolean;
  placeholder?: string;
  helpText?: string;
  validation?: ValidationRule[];
  dependencies?: FieldDependency[];
  containerClassName?: string;
  labelClassName?: string;
  inputClassName?: string;
  defaultValue?: any;
  layout?: {
    span?: number; // Grid span (1-12)
    offset?: number;
    order?: number;
  };
}

// Specific field type schemas
export interface TextFieldSchema extends BaseFieldSchema {
  type: 'text' | 'email' | 'password' | 'textarea';
  maxLength?: number;
  minLength?: number;
  rows?: number; // For textarea
}

export interface NumberFieldSchema extends BaseFieldSchema {
  type: 'number' | 'currency';
  min?: number;
  max?: number;
  step?: number;
  precision?: number;
  prefix?: string;
  suffix?: string;
}

export interface DateFieldSchema extends BaseFieldSchema {
  type: 'date' | 'datetime';
  format?: string;
  showTime?: boolean;
  disabledDate?: (date: any) => boolean;
}

export interface SelectFieldSchema extends BaseFieldSchema {
  type: 'select' | 'multiselect';
  options: SelectOption[] | (() => Promise<SelectOption[]>);
  searchable?: boolean;
  allowClear?: boolean;
  mode?: 'multiple' | 'tags';
  optionsUrl?: string; // For dynamic options loading
}

export interface CheckboxFieldSchema extends BaseFieldSchema {
  type: 'checkbox';
  checkboxText?: string;
}

export interface RadioFieldSchema extends BaseFieldSchema {
  type: 'radio';
  options: SelectOption[];
  direction?: 'horizontal' | 'vertical';
}

// Custom component schema for complex components like DocumentNumberSeqInput
export interface CustomFieldSchema extends BaseFieldSchema {
  type: 'custom';
  component: string; // Component name from registry
  props: Record<string, any>; // Props to pass to the component
  onChangeHandler?: string; // Custom onChange handler name
}

// Union type for all field schemas
export type FieldSchema = 
  | TextFieldSchema 
  | NumberFieldSchema 
  | DateFieldSchema
  | SelectFieldSchema 
  | CheckboxFieldSchema 
  | RadioFieldSchema
  | CustomFieldSchema;

export interface FormSection {
  id: string;
  title?: string;
  description?: string;
  fields: FieldSchema[];
  layout?: FormLayout;
  visible?: boolean;
  collapsible?: boolean;
  defaultCollapsed?: boolean;
  className?: string;
  dependencies?: FieldDependency[]; // Section-level dependencies
}

export interface FormValidation {
  onSubmit?: (formData: any) => Promise<boolean | string[]>;
  onChange?: (fieldId: string, value: any, formData: any) => boolean | string;
  crossFieldValidation?: (formData: any) => Record<string, string>;
}

export interface FormActions {
  submit?: {
    text?: string;
    loading?: boolean;
    disabled?: boolean;
    className?: string;
  };
  cancel?: {
    text?: string;
    onClick?: () => void;
    className?: string;
  };
  reset?: {
    text?: string;
    onClick?: () => void;
    className?: string;
  };
  custom?: Array<{
    text: string;
    onClick: (formData: any) => void;
    className?: string;
    type?: 'primary' | 'default' | 'danger';
  }>;
}

export interface FormSchema {
  id: string;
  title?: string;
  description?: string;
  sections: FormSection[];
  validation?: FormValidation;
  layout?: FormLayout;
  actions?: FormActions;
  className?: string;
  // Form-level configuration
  config?: {
    autoSave?: boolean;
    autoSaveInterval?: number;
    showProgress?: boolean;
    allowPartialSubmit?: boolean;
    resetOnSubmit?: boolean;
  };
}

// Hook return type
export interface FormRendererHookReturn {
  formData: Record<string, any>;
  errors: Record<string, string>;
  touched: Record<string, boolean>;
  loading: boolean;
  updateField: (fieldId: string, value: any) => void;
  updateMultipleFields: (updates: Record<string, any>) => void;
  validateForm: () => boolean;
  validateField: (fieldId: string) => boolean;
  resetForm: () => void;
  setFormData: (data: Record<string, any>) => void;
  setFieldError: (fieldId: string, error: string) => void;
  clearFieldError: (fieldId: string) => void;
  getFieldValue: (fieldId: string) => any;
  isFieldVisible: (fieldId: string) => boolean;
  isFieldDisabled: (fieldId: string) => boolean;
  isFieldRequired: (fieldId: string) => boolean;
}

// Component props interfaces
export interface FormRendererProps {
  schema: FormSchema;
  initialData?: Record<string, any>;
  onSubmit: (formData: Record<string, any>) => Promise<void>;
  onChange?: (formData: Record<string, any>) => void;
  loading?: boolean;
  className?: string;
  readOnly?: boolean;
}

export interface FormSectionProps {
  section: FormSection;
  formData: Record<string, any>;
  errors: Record<string, string>;
  touched: Record<string, boolean>;
  updateField: (fieldId: string, value: any) => void;
  isFieldVisible: (fieldId: string) => boolean;
  isFieldDisabled: (fieldId: string) => boolean;
  isFieldRequired: (fieldId: string) => boolean;
  readOnly?: boolean;
}

export interface FieldRendererProps {
  field: FieldSchema;
  value: any;
  error?: string;
  touched?: boolean;
  onChange: (value: any) => void;
  disabled?: boolean;
  required?: boolean;
  formData: Record<string, any>;
  readOnly?: boolean;
}
